{% extends 'base.html' %}

{% block title %}Relatórios de Uso - Portal do Cliente{% endblock %}
{% block page_title %}Relatórios{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
.report-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    text-align: center;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
}

.chart-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
}

.period-selector {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
}

.table-container {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.quality-excellent { color: var(--success-color); }
.quality-good { color: var(--info-color); }
.quality-fair { color: var(--warning-color); }
.quality-poor { color: var(--danger-color); }
</style>
{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Report Header -->
    <div class="report-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-1">Relatórios de Uso de Internet</h2>
                <p class="mb-0 opacity-75">
                    Período: {{ start_date|date:"d/m/Y" }} a {{ end_date|date:"d/m/Y" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'connections:generate_pdf_report' %}?period={{ period }}" 
                   class="btn btn-light">
                    <i class="fas fa-download me-2"></i>Baixar PDF
                </a>
            </div>
        </div>
    </div>
    
    <!-- Period Selector -->
    <div class="period-selector">
        <h5><i class="fas fa-calendar me-2"></i>Período do Relatório</h5>
        <div class="row">
            <div class="col-md-8">
                <div class="btn-group" role="group">
                    <a href="?period=week" class="btn {% if period == 'week' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        Última Semana
                    </a>
                    <a href="?period=month" class="btn {% if period == 'month' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        Último Mês
                    </a>
                    <a href="?period=quarter" class="btn {% if period == 'quarter' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        Últimos 3 Meses
                    </a>
                </div>
            </div>
            <div class="col-md-4">
                <form method="get" class="d-flex">
                    <input type="hidden" name="period" value="custom">
                    <input type="date" name="start_date" class="form-control me-2" 
                           value="{{ start_date|date:'Y-m-d' }}">
                    <input type="date" name="end_date" class="form-control me-2" 
                           value="{{ end_date|date:'Y-m-d' }}">
                    <button type="submit" class="btn btn-outline-primary">Filtrar</button>
                </form>
            </div>
        </div>
    </div>
    
    {% if error %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
    </div>
    {% else %}
    
    <!-- Statistics Cards -->
    {% if usage_stats %}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">{{ usage_stats.total_download_gb|floatformat:1 }}</div>
                <div class="stat-label">GB Download</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">{{ usage_stats.total_upload_gb|floatformat:1 }}</div>
                <div class="stat-label">GB Upload</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">{{ usage_stats.avg_daily_gb|floatformat:1 }}</div>
                <div class="stat-label">Média Diária (GB)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">{{ usage_stats.peak_usage_gb|floatformat:1 }}</div>
                <div class="stat-label">Pico de Uso (GB)</div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Usage Chart -->
    {% if chart_data.usage %}
    <div class="chart-container">
        <h5><i class="fas fa-chart-area me-2"></i>Uso Diário de Internet</h5>
        <canvas id="usageChart" height="100"></canvas>
    </div>
    {% endif %}
    
    <!-- Speed Statistics -->
    {% if speed_stats %}
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stat-card">
                <div class="stat-value">{{ speed_stats.avg_download|floatformat:1 }}</div>
                <div class="stat-label">Download Médio (Mbps)</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stat-card">
                <div class="stat-value">{{ speed_stats.avg_upload|floatformat:1 }}</div>
                <div class="stat-label">Upload Médio (Mbps)</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stat-card">
                <div class="stat-value">{{ speed_stats.avg_ping|floatformat:1 }}</div>
                <div class="stat-label">Ping Médio (ms)</div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Speed Chart -->
    {% if chart_data.speed %}
    <div class="chart-container">
        <h5><i class="fas fa-tachometer-alt me-2"></i>Histórico de Velocidade</h5>
        <canvas id="speedChart" height="100"></canvas>
    </div>
    {% endif %}
    
    <!-- Speed Tests Table -->
    {% if speed_tests %}
    <div class="table-container">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Data/Hora</th>
                        <th>Download</th>
                        <th>Upload</th>
                        <th>Ping</th>
                        <th>Qualidade</th>
                        <th>Eficiência</th>
                    </tr>
                </thead>
                <tbody>
                    {% for test in speed_tests %}
                    <tr>
                        <td>{{ test.test_date|date:"d/m/Y H:i" }}</td>
                        <td>
                            <strong>{{ test.download_speed|floatformat:1 }} Mbps</strong>
                            <small class="text-muted d-block">{{ test.download_percentage|floatformat:0 }}% do plano</small>
                        </td>
                        <td>
                            <strong>{{ test.upload_speed|floatformat:1 }} Mbps</strong>
                            <small class="text-muted d-block">{{ test.upload_percentage|floatformat:0 }}% do plano</small>
                        </td>
                        <td>{{ test.ping|floatformat:1 }} ms</td>
                        <td>
                            <span class="quality-{{ test.quality_rating|lower }}">
                                <i class="fas fa-circle me-1"></i>{{ test.get_quality_display }}
                            </span>
                        </td>
                        <td>
                            {% if test.download_percentage >= 80 %}
                                <span class="text-success"><i class="fas fa-check-circle"></i> Ótima</span>
                            {% elif test.download_percentage >= 60 %}
                                <span class="text-warning"><i class="fas fa-exclamation-circle"></i> Boa</span>
                            {% else %}
                                <span class="text-danger"><i class="fas fa-times-circle"></i> Baixa</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="chart-container text-center">
        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
        <h5>Nenhum teste de velocidade encontrado</h5>
        <p class="text-muted">Faça alguns testes de velocidade para ver relatórios detalhados.</p>
        <a href="{% url 'connections:speed_test' %}" class="btn btn-primary">
            <i class="fas fa-tachometer-alt me-2"></i>Fazer Teste de Velocidade
        </a>
    </div>
    {% endif %}
    
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Usage Chart
    {% if chart_data.usage %}
    const usageCtx = document.getElementById('usageChart').getContext('2d');
    new Chart(usageCtx, {
        type: 'line',
        data: {
            labels: {{ chart_data.usage.labels|safe }},
            datasets: [{
                label: 'Download (GB)',
                data: {{ chart_data.usage.download_data|safe }},
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                fill: true,
                tension: 0.4
            }, {
                label: 'Upload (GB)',
                data: {{ chart_data.usage.upload_data|safe }},
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Uso (GB)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Data'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            }
        }
    });
    {% endif %}
    
    // Speed Chart
    {% if chart_data.speed %}
    const speedCtx = document.getElementById('speedChart').getContext('2d');
    new Chart(speedCtx, {
        type: 'line',
        data: {
            labels: {{ chart_data.speed.labels|safe }},
            datasets: [{
                label: 'Download (Mbps)',
                data: {{ chart_data.speed.download_speeds|safe }},
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                yAxisID: 'y'
            }, {
                label: 'Upload (Mbps)',
                data: {{ chart_data.speed.upload_speeds|safe }},
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                yAxisID: 'y'
            }, {
                label: 'Ping (ms)',
                data: {{ chart_data.speed.ping_values|safe }},
                borderColor: 'rgb(245, 158, 11)',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Data/Hora'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Velocidade (Mbps)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Ping (ms)'
                    },
                    grid: {
                        drawOnChartArea: false
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
    {% endif %}
});
</script>
{% endblock %}
