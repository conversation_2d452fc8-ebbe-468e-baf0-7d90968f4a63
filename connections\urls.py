"""
URLs para o sistema de conexões e uso de internet.

Define as rotas para visualização de dados de conexão,
histórico de uso e relatórios de banda.
"""

from django.urls import path
from . import views

app_name = 'connections'

urlpatterns = [
    # Dashboard principal de conexões
    path('', views.ConnectionDashboardView.as_view(), name='dashboard'),

    # Teste de velocidade
    path('velocidade/', views.SpeedTestView.as_view(), name='speed_test'),
    path('api/run-speed-test/', views.run_speed_test, name='run_speed_test'),
    path('api/speed-test-history/', views.speed_test_history, name='speed_test_history'),

    # Relatórios
    path('relatorios/', views.BandwidthReportsView.as_view(), name='reports'),
    path('relatorios/pdf/', views.generate_pdf_report, name='generate_pdf_report'),

    # URLs para funcionalidades futuras
    # path('historico/', views.ConnectionHistoryView.as_view(), name='history'),
    # path('api/usage-chart/', views.usage_chart_api, name='usage_chart_api'),
]
