<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Portal do Cliente - Provedor{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="{% if not user.is_authenticated %}login-page{% endif %}"">
    {% if user.is_authenticated %}
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-wifi"></i>
                <span class="brand-text">Portal Cliente</span>
            </div>
            <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}"
                       href="{% url 'customer_portal:dashboard' %}">
                        <i class="fas fa-home"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'billing' in request.resolver_match.namespace %}active{% endif %}"
                       href="{% url 'billing:invoice_list' %}">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>Faturas</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'support' in request.resolver_match.namespace %}active{% endif %}"
                       href="{% url 'support:ticket_list' %}">
                        <i class="fas fa-headset"></i>
                        <span>Suporte</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'connections' in request.resolver_match.namespace %}active{% endif %}"
                       href="{% url 'connections:dashboard' %}">
                        <i class="fas fa-chart-line"></i>
                        <span>Conexões</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{{ user.first_name }} {{ user.last_name }}</div>
                    <div class="user-email">{{ user.email }}</div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-link user-menu-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'customer_portal:profile' %}"><i class="fas fa-user-cog me-2"></i>Perfil</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Configurações</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{% url 'authentication:logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>Sair
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Bar -->
    <div class="topbar">
        <div class="topbar-left">
            <button class="sidebar-toggle d-lg-none" id="sidebarToggleMobile">
                <i class="fas fa-bars"></i>
            </button>
            <div class="page-title">
                <h1>{% block page_title %}Dashboard{% endblock %}</h1>
            </div>
        </div>
        <div class="topbar-right">
            <div class="topbar-actions">
                <div class="dropdown">
                    <button class="btn btn-link notification-btn" title="Notificações" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        {% with unread_count=user.notifications.unread.count %}
                        {% if unread_count > 0 %}
                        <span class="notification-badge">{{ unread_count }}</span>
                        {% endif %}
                        {% endwith %}
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <div class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>Notificações</span>
                            {% with unread_count=user.notifications.unread.count %}
                            <small class="text-muted">{{ unread_count }} não lidas</small>
                            {% endwith %}
                        </div>
                        <div class="dropdown-divider"></div>
                        {% for notification in user.notifications.recent %}
                        <a class="dropdown-item notification-item {% if not notification.is_read %}unread{% endif %}"
                           href="{% if notification.url %}{{ notification.url }}{% else %}#{% endif %}">
                            <div class="d-flex">
                                <div class="notification-icon me-2">
                                    <i class="{{ notification.icon_class }}"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="notification-title">{{ notification.title }}</div>
                                    <div class="notification-message">{{ notification.message|truncatechars:50 }}</div>
                                    <small class="text-muted">{{ notification.created_at|timesince }} atrás</small>
                                </div>
                            </div>
                        </a>
                        {% empty %}
                        <div class="dropdown-item-text text-center text-muted py-3">
                            <i class="fas fa-bell-slash fa-2x mb-2"></i>
                            <div>Nenhuma notificação</div>
                        </div>
                        {% endfor %}
                        {% if user.notifications.recent.count > 5 %}
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item text-center" href="#">
                            Ver todas as notificações
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-link" title="Buscar" data-bs-toggle="dropdown">
                        <i class="fas fa-search"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end search-dropdown">
                        <div class="dropdown-header">
                            <div class="input-group">
                                <input type="text" id="globalSearchInput" class="form-control"
                                       placeholder="Buscar faturas, tickets..." autocomplete="off">
                                <button class="btn btn-outline-primary" type="button" id="searchButton">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div id="searchResults" class="search-results" style="display: none;">
                            <!-- Resultados da busca aparecerão aqui -->
                        </div>
                        <div class="dropdown-item-text text-center text-muted py-3" id="searchPlaceholder">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <div>Digite para buscar</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-wrapper">
            <!-- Messages -->
            {% if messages %}
            <div class="messages-container">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Page Content -->
            <div class="page-content">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    {% else %}
    <!-- Login Page Content -->
    <div class="login-container">
        {% if messages %}
        <div class="messages-container">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block login_content %}{% endblock %}
    </div>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggleMobile = document.getElementById('sidebarToggleMobile');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggleMobile) {
                sidebarToggleMobile.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 992) {
                    if (!sidebar.contains(event.target) && !sidebarToggleMobile.contains(event.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });

        // Global Search functionality
        const searchInput = document.getElementById('globalSearchInput');
        const searchButton = document.getElementById('searchButton');
        const searchResults = document.getElementById('searchResults');
        const searchPlaceholder = document.getElementById('searchPlaceholder');

        if (searchInput) {
            let searchTimeout;

            function performSearch(query) {
                if (query.length < 2) {
                    searchResults.style.display = 'none';
                    searchPlaceholder.style.display = 'block';
                    return;
                }

                fetch(`{% url 'customer_portal:search' %}?q=${encodeURIComponent(query)}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayResults(data.results);
                    }
                })
                .catch(error => {
                    console.error('Erro na busca:', error);
                });
            }

            function displayResults(results) {
                let html = '';

                // Faturas
                if (results.invoices.length > 0) {
                    html += '<div class="search-result-section">Faturas</div>';
                    results.invoices.forEach(invoice => {
                        html += `
                            <a href="${invoice.url}" class="search-result-item d-block">
                                <div class="search-result-title">Fatura #${invoice.id}</div>
                                <div class="search-result-meta">R$ ${invoice.amount} - Vence em ${invoice.due_date}</div>
                            </a>
                        `;
                    });
                }

                // Tickets
                if (results.tickets.length > 0) {
                    html += '<div class="search-result-section">Chamados</div>';
                    results.tickets.forEach(ticket => {
                        html += `
                            <a href="${ticket.url}" class="search-result-item d-block">
                                <div class="search-result-title">${ticket.title}</div>
                                <div class="search-result-meta">Status: ${ticket.status} - ${ticket.created_at}</div>
                            </a>
                        `;
                    });
                }

                if (html === '') {
                    html = '<div class="dropdown-item-text text-center text-muted py-3">Nenhum resultado encontrado</div>';
                }

                searchResults.innerHTML = html;
                searchResults.style.display = 'block';
                searchPlaceholder.style.display = 'none';
            }

            // Event listeners
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            });

            searchButton.addEventListener('click', function() {
                const query = searchInput.value.trim();
                performSearch(query);
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = this.value.trim();
                    performSearch(query);
                }
            });
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
