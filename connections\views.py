"""
Views para o sistema de conexões e uso de internet.

Este módulo implementa as views para visualização de dados de conexão,
histórico de uso, testes de velocidade e relatórios de banda.
"""

from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.db.models import Su<PERSON>, Avg, <PERSON>, <PERSON>, Count
from django.utils import timezone
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging
import json
import random
import time

from integrations.services import ConnectionService, CustomerService, IXCSoftAPIError
from .models import ConnectionSession, SpeedTest, BandwidthUsage

logger = logging.getLogger(__name__)


class ConnectionDashboardView(LoginRequiredMixin, TemplateView):
    """
    Dashboard principal de conexões.

    Mostra resumo de uso, estatísticas e gráficos
    de conexão do cliente.
    """

    template_name = 'connections/dashboard.html'

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        """Adiciona dados de conexão ao contexto"""
        context = super().get_context_data(**kwargs)

        try:
            # Verifica se cliente tem ID na IXCSoft
            if not self.request.user.ixc_customer_id:
                self._ensure_customer_id()

            if not self.request.user.ixc_customer_id:
                context.update({
                    'error': 'Dados do cliente não encontrados no sistema.',
                    'title': 'Conexões e Uso'
                })
                return context

            # Busca dados via API e cache local
            connection_service = ConnectionService()

            # Resumo de uso do mês atual
            usage_summary = connection_service.get_customer_usage_summary(
                self.request.user.ixc_customer_id,
                'month'
            )

            # Detalhes do plano
            plan_details = connection_service.get_customer_plan_details(
                self.request.user.ixc_customer_id
            )

            # Últimos testes de velocidade
            recent_speed_tests = connection_service.get_customer_speed_tests(
                self.request.user.ixc_customer_id,
                limit=5
            )

            # Sincroniza dados locais
            self._sync_speed_tests(recent_speed_tests)

            # Estatísticas locais
            local_stats = self._get_local_statistics()

            context.update({
                'usage_summary': usage_summary,
                'plan_details': plan_details,
                'recent_speed_tests': recent_speed_tests,
                'local_stats': local_stats,
                'title': 'Conexões e Uso'
            })

        except IXCSoftAPIError as e:
            logger.error(f"Erro na API IXCSoft: {e}")
            messages.error(
                self.request,
                'Erro ao carregar dados de conexão. Tente novamente.'
            )
            context['error'] = 'Erro ao carregar dados da API.'

        except Exception as e:
            logger.error(f"Erro inesperado em ConnectionDashboardView: {e}")
            messages.error(
                self.request,
                'Erro interno. Contate o suporte.'
            )
            context['error'] = 'Erro interno do sistema.'

        return context

    def _ensure_customer_id(self) -> None:
        """Garante que o cliente tem ID da IXCSoft"""
        if not self.request.user.ixc_customer_id:
            customer_service = CustomerService()
            customer_data = customer_service.get_customer_by_cpf(
                self.request.user.cpf_clean
            )
            if customer_data:
                self.request.user.ixc_customer_id = customer_data.get('id')
                self.request.user.save(update_fields=['ixc_customer_id'])

    def _sync_speed_tests(self, api_tests: List[Dict]) -> None:
        """Sincroniza testes de velocidade com cache local"""
        try:
            for test_data in api_tests:
                test_id = test_data.get('id')
                if not test_id:
                    continue

                SpeedTest.objects.get_or_create(
                    ixc_test_id=str(test_id),
                    customer=self.request.user,
                    defaults={
                        'test_date': self._parse_datetime(test_data.get('data_teste')),
                        'download_speed': float(test_data.get('velocidade_download', 0)),
                        'upload_speed': float(test_data.get('velocidade_upload', 0)),
                        'ping': float(test_data.get('ping', 0)),
                        'server_name': test_data.get('servidor', ''),
                        'contracted_download': float(test_data.get('plano_download', 0)),
                        'contracted_upload': float(test_data.get('plano_upload', 0)),
                    }
                )
        except Exception as e:
            logger.error(f"Erro ao sincronizar testes de velocidade: {e}")

    def _get_local_statistics(self) -> Dict[str, Any]:
        """Calcula estatísticas dos dados locais"""
        try:
            # Últimos 30 dias
            thirty_days_ago = timezone.now() - timedelta(days=30)

            # Estatísticas de testes de velocidade
            recent_tests = SpeedTest.objects.filter(
                customer=self.request.user,
                test_date__gte=thirty_days_ago
            )

            speed_stats = recent_tests.aggregate(
                avg_download=Avg('download_speed'),
                avg_upload=Avg('upload_speed'),
                avg_ping=Avg('ping'),
                max_download=Max('download_speed'),
                max_upload=Max('upload_speed'),
                count=Count('id')
            )

            # Estatísticas de sessões
            recent_sessions = ConnectionSession.objects.filter(
                customer=self.request.user,
                start_time__gte=thirty_days_ago
            )

            session_stats = recent_sessions.aggregate(
                total_sessions=Count('id'),
                total_bytes=Sum('bytes_uploaded') + Sum('bytes_downloaded'),
                avg_duration=Avg('duration_seconds'),
                total_duration=Sum('duration_seconds')
            )

            return {
                'speed_stats': speed_stats,
                'session_stats': session_stats,
                'period_days': 30
            }

        except Exception as e:
            logger.error(f"Erro ao calcular estatísticas locais: {e}")
            return {}

    def _parse_datetime(self, date_string: str) -> datetime:
        """Converte string de data para datetime"""
        if not date_string:
            return timezone.now()

        try:
            # Tenta diferentes formatos
            for fmt in ['%Y-%m-%d %H:%M:%S', '%d/%m/%Y %H:%M:%S', '%Y-%m-%d']:
                try:
                    return datetime.strptime(date_string, fmt)
                except ValueError:
                    continue

            return timezone.now()

        except Exception:
            return timezone.now()


class SpeedTestView(LoginRequiredMixin, TemplateView):
    """
    View para teste de velocidade da internet.

    Permite ao usuário executar testes de velocidade
    e visualizar o histórico de testes realizados.
    """

    template_name = 'connections/speed_test.html'

    def get_context_data(self, **kwargs):
        """Adiciona dados do teste de velocidade ao contexto"""
        context = super().get_context_data(**kwargs)

        try:
            # Buscar histórico de testes
            recent_tests = SpeedTest.objects.filter(
                customer=self.request.user
            ).order_by('-test_date')[:10]

            # Estatísticas dos testes
            if recent_tests:
                from django.db.models import Min
                stats = recent_tests.aggregate(
                    avg_download=Avg('download_speed'),
                    avg_upload=Avg('upload_speed'),
                    avg_ping=Avg('ping'),
                    max_download=Max('download_speed'),
                    max_upload=Max('upload_speed'),
                    min_ping=Min('ping')
                )
            else:
                stats = {}

            # Buscar informações do plano
            customer_service = CustomerService()
            plan_details = customer_service.get_customer_plan_details(
                self.request.user.ixc_customer_id
            )

            context.update({
                'recent_tests': recent_tests,
                'test_stats': stats,
                'plan_details': plan_details,
                'title': 'Teste de Velocidade'
            })

        except Exception as e:
            logger.error(f"Erro ao carregar dados do teste de velocidade: {e}")
            context['error'] = "Erro ao carregar dados do teste de velocidade"

        return context


@login_required
def run_speed_test(request):
    """
    API para executar teste de velocidade.

    Simula um teste de velocidade real e salva os resultados.
    Em produção, seria integrado com uma API real de teste.
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Método não permitido'}, status=405)

    try:
        # Simular teste de velocidade (em produção seria uma API real)
        # Aqui vamos simular latências e variações realísticas

        # Buscar plano do cliente para base de cálculo
        customer_service = CustomerService()
        plan_details = customer_service.get_customer_plan_details(
            request.user.ixc_customer_id
        )

        # Velocidades base do plano (com variação realística)
        base_download = plan_details.get('velocidade_download', 100) if plan_details else 100
        base_upload = plan_details.get('velocidade_upload', 50) if plan_details else 50

        # Simular variações realísticas (70% a 110% da velocidade contratada)
        download_variation = random.uniform(0.7, 1.1)
        upload_variation = random.uniform(0.8, 1.2)

        download_speed = round(base_download * download_variation, 2)
        upload_speed = round(base_upload * upload_variation, 2)

        # Ping realístico (10-80ms)
        ping = round(random.uniform(10, 80), 1)

        # Jitter baseado no ping
        jitter = round(ping * random.uniform(0.1, 0.3), 1)

        # Simular tempo de teste (3-8 segundos)
        test_duration = random.uniform(3, 8)
        time.sleep(min(test_duration, 2))  # Simular apenas 2s para não travar a UI

        # Salvar resultado do teste
        speed_test = SpeedTest.objects.create(
            customer=request.user,
            test_date=timezone.now(),
            server_name='Servidor de Teste Local',
            server_location='São Paulo, SP',
            download_speed=download_speed,
            upload_speed=upload_speed,
            ping=ping,
            jitter=jitter,
            contracted_download=base_download,
            contracted_upload=base_upload,
            ip_address=request.META.get('REMOTE_ADDR', '127.0.0.1')
        )

        # Criar notificação sobre o teste
        from customer_portal.models import Notification
        Notification.create_notification(
            user=request.user,
            title='Teste de Velocidade Concluído',
            message=f'Download: {download_speed:.1f}MB/s, Upload: {upload_speed:.1f}MB/s, Ping: {ping:.1f}ms',
            type='success',
            category='connection',
            url='/conexoes/velocidade/'
        )

        return JsonResponse({
            'success': True,
            'test_id': speed_test.id,
            'results': {
                'download_speed': download_speed,
                'upload_speed': upload_speed,
                'ping': ping,
                'jitter': jitter,
                'download_percentage': speed_test.download_percentage,
                'upload_percentage': speed_test.upload_percentage,
                'quality_rating': speed_test.quality_rating,
                'test_date': speed_test.test_date.isoformat(),
                'server_name': speed_test.server_name
            }
        })

    except Exception as e:
        logger.error(f"Erro ao executar teste de velocidade: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Erro interno do servidor'
        }, status=500)


@login_required
def speed_test_history(request):
    """
    API para buscar histórico de testes de velocidade.

    Retorna os testes em formato JSON para gráficos e tabelas.
    """
    try:
        # Parâmetros de filtro
        days = int(request.GET.get('days', 30))
        limit = int(request.GET.get('limit', 50))

        # Buscar testes do período
        start_date = timezone.now() - timedelta(days=days)
        tests = SpeedTest.objects.filter(
            customer=request.user,
            test_date__gte=start_date
        ).order_by('-test_date')[:limit]

        # Converter para JSON
        tests_data = []
        for test in tests:
            tests_data.append({
                'id': test.id,
                'test_date': test.test_date.isoformat(),
                'download_speed': test.download_speed,
                'upload_speed': test.upload_speed,
                'ping': test.ping,
                'jitter': test.jitter,
                'quality_rating': test.quality_rating,
                'server_name': test.server_name,
                'download_percentage': test.download_percentage,
                'upload_percentage': test.upload_percentage
            })

        return JsonResponse({
            'success': True,
            'tests': tests_data,
            'total': len(tests_data)
        })

    except Exception as e:
        logger.error(f"Erro ao buscar histórico de testes: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Erro ao buscar histórico'
        }, status=500)


class BandwidthReportsView(LoginRequiredMixin, TemplateView):
    """
    View para relatórios de uso de banda.

    Gera relatórios detalhados de uso de internet
    com gráficos e estatísticas avançadas.
    """

    template_name = 'connections/reports.html'

    def get_context_data(self, **kwargs):
        """Adiciona dados dos relatórios ao contexto"""
        context = super().get_context_data(**kwargs)

        try:
            # Parâmetros de filtro
            period = self.request.GET.get('period', 'month')
            start_date = self.request.GET.get('start_date')
            end_date = self.request.GET.get('end_date')

            # Calcular período
            if period == 'week':
                start = timezone.now() - timedelta(days=7)
                end = timezone.now()
            elif period == 'month':
                start = timezone.now() - timedelta(days=30)
                end = timezone.now()
            elif period == 'quarter':
                start = timezone.now() - timedelta(days=90)
                end = timezone.now()
            elif period == 'custom' and start_date and end_date:
                start = timezone.datetime.strptime(start_date, '%Y-%m-%d')
                end = timezone.datetime.strptime(end_date, '%Y-%m-%d')
            else:
                start = timezone.now() - timedelta(days=30)
                end = timezone.now()

            # Buscar dados de uso
            connection_service = ConnectionService()

            # Dados de uso diário
            daily_usage = connection_service.get_customer_daily_usage(
                self.request.user.ixc_customer_id,
                start.date(),
                end.date()
            )

            # Estatísticas do período
            usage_stats = connection_service.get_customer_usage_stats(
                self.request.user.ixc_customer_id,
                start.date(),
                end.date()
            )

            # Testes de velocidade do período
            speed_tests = SpeedTest.objects.filter(
                customer=self.request.user,
                test_date__range=[start, end]
            ).order_by('-test_date')

            # Estatísticas de velocidade
            if speed_tests:
                speed_stats = speed_tests.aggregate(
                    avg_download=Avg('download_speed'),
                    avg_upload=Avg('upload_speed'),
                    avg_ping=Avg('ping'),
                    max_download=Max('download_speed'),
                    max_upload=Max('upload_speed'),
                    min_ping=Min('ping'),
                    test_count=Count('id')
                )
            else:
                speed_stats = {}

            # Dados para gráficos
            chart_data = self._prepare_chart_data(daily_usage, speed_tests)

            context.update({
                'period': period,
                'start_date': start.date(),
                'end_date': end.date(),
                'daily_usage': daily_usage,
                'usage_stats': usage_stats,
                'speed_tests': speed_tests[:10],  # Últimos 10 testes
                'speed_stats': speed_stats,
                'chart_data': chart_data,
                'title': 'Relatórios de Uso'
            })

        except Exception as e:
            logger.error(f"Erro ao gerar relatórios: {e}")
            context['error'] = "Erro ao carregar dados dos relatórios"

        return context

    def _prepare_chart_data(self, daily_usage, speed_tests):
        """Prepara dados para os gráficos"""
        try:
            # Dados de uso diário para gráfico
            usage_chart = {
                'labels': [],
                'download_data': [],
                'upload_data': []
            }

            for usage in daily_usage:
                usage_chart['labels'].append(usage['date'].strftime('%d/%m'))
                usage_chart['download_data'].append(usage.get('download_gb', 0))
                usage_chart['upload_data'].append(usage.get('upload_gb', 0))

            # Dados de velocidade para gráfico
            speed_chart = {
                'labels': [],
                'download_speeds': [],
                'upload_speeds': [],
                'ping_values': []
            }

            for test in speed_tests[:30]:  # Últimos 30 testes
                speed_chart['labels'].append(test.test_date.strftime('%d/%m %H:%M'))
                speed_chart['download_speeds'].append(test.download_speed)
                speed_chart['upload_speeds'].append(test.upload_speed)
                speed_chart['ping_values'].append(test.ping)

            return {
                'usage': usage_chart,
                'speed': speed_chart
            }

        except Exception as e:
            logger.error(f"Erro ao preparar dados do gráfico: {e}")
            return {'usage': {}, 'speed': {}}


@login_required
def generate_pdf_report(request):
    """
    Gera relatório em PDF para download.

    Cria um relatório completo em PDF com
    gráficos e estatísticas de uso.
    """
    try:
        from django.template.loader import render_to_string
        from django.http import HttpResponse
        import io

        # Buscar dados do relatório
        period = request.GET.get('period', 'month')

        # Calcular período
        if period == 'week':
            start = timezone.now() - timedelta(days=7)
            end = timezone.now()
        elif period == 'month':
            start = timezone.now() - timedelta(days=30)
            end = timezone.now()
        else:
            start = timezone.now() - timedelta(days=30)
            end = timezone.now()

        # Buscar dados
        connection_service = ConnectionService()

        usage_stats = connection_service.get_customer_usage_stats(
            request.user.ixc_customer_id,
            start.date(),
            end.date()
        )

        speed_tests = SpeedTest.objects.filter(
            customer=request.user,
            test_date__range=[start, end]
        ).order_by('-test_date')[:10]

        # Contexto para o template
        context = {
            'user': request.user,
            'period': period,
            'start_date': start.date(),
            'end_date': end.date(),
            'usage_stats': usage_stats,
            'speed_tests': speed_tests,
            'generated_at': timezone.now()
        }

        # Renderizar HTML
        html_content = render_to_string('connections/report_pdf.html', context)

        # Simular geração de PDF (em produção usaria WeasyPrint ou similar)
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="relatorio_uso_{period}_{timezone.now().strftime("%Y%m%d")}.pdf"'

        # Por enquanto, retornar HTML como PDF simulado
        response.write(html_content.encode('utf-8'))

        return response

    except Exception as e:
        logger.error(f"Erro ao gerar PDF: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Erro ao gerar relatório PDF'
        }, status=500)
