"""
WSGI config for provedor project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'provedor.settings')

# RAILWAY FIX - Força ALLOWED_HOSTS antes da aplicação inicializar
print("=== RAILWAY WSGI FIX ===")
print("Forçando ALLOWED_HOSTS no WSGI...")

application = get_wsgi_application()

# Força ALLOWED_HOSTS após a aplicação ser criada
from django.conf import settings
settings.ALLOWED_HOSTS = ['*']
print(f"ALLOWED_HOSTS forçado para: {settings.ALLOWED_HOSTS}")
print("=== FIM RAILWAY WSGI FIX ===")
