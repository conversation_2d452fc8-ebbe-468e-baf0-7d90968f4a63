from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import Customer


@admin.register(Customer)
class CustomerAdmin(UserAdmin):
    """Admin para o modelo Customer"""

    list_display = ('cpf', 'first_name', 'last_name', 'email', 'is_active_customer', 'is_staff')
    list_filter = ('is_active_customer', 'is_staff', 'is_superuser', 'date_joined')
    search_fields = ('cpf', 'first_name', 'last_name', 'email')
    ordering = ('cpf',)

    fieldsets = (
        (None, {'fields': ('cpf', 'password')}),
        ('Informações Pessoais', {'fields': ('first_name', 'last_name', 'email', 'phone')}),
        ('Permissões', {
            'fields': ('is_active', 'is_active_customer', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        ('Datas Importantes', {'fields': ('last_login', 'date_joined', 'first_login')}),
        ('IXCSoft', {'fields': ('ixc_customer_id',)}),
        ('Notificações', {'fields': ('receive_email_notifications', 'receive_push_notifications', 'fcm_token')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('cpf', 'first_name', 'last_name', 'email', 'password1', 'password2'),
        }),
    )

    readonly_fields = ('date_joined', 'last_login')
