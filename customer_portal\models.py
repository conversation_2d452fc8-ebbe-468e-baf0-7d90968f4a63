"""
Modelos para o portal do cliente.

Este módulo implementa os modelos para notificações
e outras funcionalidades do portal.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class NotificationManager(models.Manager):
    """Manager customizado para notificações"""

    def unread(self):
        """Retorna notificações não lidas"""
        return self.filter(is_read=False).exclude(
            expires_at__lt=timezone.now()
        )

    def recent(self, limit=10):
        """Retorna notificações recentes"""
        return self.exclude(
            expires_at__lt=timezone.now()
        ).order_by('-created_at')[:limit]


class Notification(models.Model):
    """
    Modelo para notificações do sistema.

    Armazena notificações para os usuários sobre
    faturas, suporte, conexões e outras atividades.
    """

    TYPE_CHOICES = [
        ('info', 'Informação'),
        ('success', 'Sucesso'),
        ('warning', 'Aviso'),
        ('error', 'Erro'),
    ]

    CATEGORY_CHOICES = [
        ('billing', 'Faturas'),
        ('support', 'Suporte'),
        ('connection', 'Conexão'),
        ('system', 'Sistema'),
        ('account', 'Conta'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        help_text='Usuário que receberá a notificação'
    )

    title = models.CharField(
        max_length=200,
        help_text='Título da notificação'
    )

    message = models.TextField(
        help_text='Mensagem da notificação'
    )

    type = models.CharField(
        max_length=10,
        choices=TYPE_CHOICES,
        default='info',
        help_text='Tipo da notificação'
    )

    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='system',
        help_text='Categoria da notificação'
    )

    is_read = models.BooleanField(
        default=False,
        help_text='Se a notificação foi lida'
    )

    url = models.URLField(
        blank=True,
        help_text='URL para ação relacionada (opcional)'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text='Data de criação'
    )

    read_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Data de leitura'
    )

    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Data de expiração (opcional)'
    )

    # Manager customizado
    objects = NotificationManager()

    class Meta:
        verbose_name = 'Notificação'
        verbose_name_plural = 'Notificações'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['created_at']),
            models.Index(fields=['category']),
        ]

    def __str__(self) -> str:
        return f"{self.title} - {self.user.first_name}"

    def mark_as_read(self):
        """Marca a notificação como lida"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

    @property
    def is_expired(self) -> bool:
        """Verifica se a notificação expirou"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def icon_class(self) -> str:
        """Retorna a classe do ícone baseada no tipo"""
        icons = {
            'info': 'fas fa-info-circle',
            'success': 'fas fa-check-circle',
            'warning': 'fas fa-exclamation-triangle',
            'error': 'fas fa-times-circle',
        }
        return icons.get(self.type, 'fas fa-bell')

    @property
    def badge_class(self) -> str:
        """Retorna a classe do badge baseada no tipo"""
        badges = {
            'info': 'bg-info',
            'success': 'bg-success',
            'warning': 'bg-warning',
            'error': 'bg-danger',
        }
        return badges.get(self.type, 'bg-secondary')

    @classmethod
    def create_notification(cls, user, title, message, type='info', category='system', url=None, expires_days=None):
        """
        Método helper para criar notificações.

        Args:
            user: Usuário que receberá a notificação
            title: Título da notificação
            message: Mensagem da notificação
            type: Tipo da notificação (info, success, warning, error)
            category: Categoria da notificação
            url: URL opcional para ação
            expires_days: Dias para expiração (opcional)

        Returns:
            Notification: A notificação criada
        """
        expires_at = None
        if expires_days:
            expires_at = timezone.now() + timezone.timedelta(days=expires_days)

        return cls.objects.create(
            user=user,
            title=title,
            message=message,
            type=type,
            category=category,
            url=url or '',
            expires_at=expires_at
        )

    @classmethod
    def get_unread_count(cls, user):
        """Retorna o número de notificações não lidas do usuário"""
        return cls.objects.filter(
            user=user,
            is_read=False
        ).exclude(
            expires_at__lt=timezone.now()
        ).count()

    @classmethod
    def get_recent_notifications(cls, user, limit=10):
        """Retorna as notificações recentes do usuário"""
        return cls.objects.filter(
            user=user
        ).exclude(
            expires_at__lt=timezone.now()
        )[:limit]
