from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.core.validators import RegexValidator
import re


def validate_cpf(cpf):
    """Valida CPF brasileiro"""
    # Remove caracteres não numéricos
    cpf = re.sub(r'[^0-9]', '', cpf)

    # Verifica se tem 11 dígitos
    if len(cpf) != 11:
        raise ValueError("CPF deve ter 11 dígitos")

    # Verifica se não são todos iguais
    if cpf == cpf[0] * 11:
        raise ValueError("CPF inválido")

    # Calcula primeiro dígito verificador
    soma = sum(int(cpf[i]) * (10 - i) for i in range(9))
    resto = soma % 11
    digito1 = 0 if resto < 2 else 11 - resto

    # Calcula segundo dígito verificador
    soma = sum(int(cpf[i]) * (11 - i) for i in range(10))
    resto = soma % 11
    digito2 = 0 if resto < 2 else 11 - resto

    # Verifica se os dígitos estão corretos
    if int(cpf[9]) != digito1 or int(cpf[10]) != digito2:
        raise ValueError("CPF inválido")

    return cpf


class CustomerManager(BaseUserManager):
    """Manager customizado para o modelo Customer"""

    def create_user(self, cpf, email, password=None, **extra_fields):
        """Cria e salva um usuário comum"""
        if not cpf:
            raise ValueError('O CPF é obrigatório')
        if not email:
            raise ValueError('O email é obrigatório')

        email = self.normalize_email(email)
        user = self.model(cpf=cpf, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, cpf, email, password=None, **extra_fields):
        """Cria e salva um superusuário"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser deve ter is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser deve ter is_superuser=True.')

        return self.create_user(cpf, email, password, **extra_fields)


class Customer(AbstractUser):
    """Modelo de usuário customizado para clientes do provedor"""

    # Remove o campo username padrão
    username = None

    # CPF será usado como identificador único
    cpf = models.CharField(
        max_length=14,
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$',
                message='CPF deve estar no formato XXX.XXX.XXX-XX ou apenas números'
            )
        ],
        help_text='CPF do cliente (com ou sem formatação)'
    )

    # Campos adicionais do cliente
    phone = models.CharField(
        max_length=15,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\(\d{2}\)\s\d{4,5}-\d{4}$|^\d{10,11}$',
                message='Telefone deve estar no formato (XX) XXXXX-XXXX'
            )
        ]
    )

    # Dados do cliente na IXCSoft
    ixc_customer_id = models.IntegerField(
        null=True,
        blank=True,
        help_text='ID do cliente no sistema IXCSoft'
    )

    # Status da conta
    is_active_customer = models.BooleanField(
        default=True,
        help_text='Cliente ativo no sistema'
    )

    # Data de primeira autenticação
    first_login = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Data do primeiro login do cliente'
    )

    # Configurações de notificação
    receive_email_notifications = models.BooleanField(
        default=True,
        help_text='Receber notificações por email'
    )

    receive_push_notifications = models.BooleanField(
        default=True,
        help_text='Receber notificações push'
    )

    # Token FCM para notificações push
    fcm_token = models.TextField(
        blank=True,
        help_text='Token FCM para notificações push'
    )

    # Manager customizado
    objects = CustomerManager()

    # Define CPF como campo de login
    USERNAME_FIELD = 'cpf'
    REQUIRED_FIELDS = ['email', 'first_name', 'last_name']

    class Meta:
        verbose_name = 'Cliente'
        verbose_name_plural = 'Clientes'
        db_table = 'auth_customer'

    def save(self, *args, **kwargs):
        # Limpa e valida CPF antes de salvar
        if self.cpf:
            # Remove formatação
            cpf_clean = re.sub(r'[^0-9]', '', self.cpf)
            # Valida CPF
            validate_cpf(cpf_clean)
            # Salva com formatação
            self.cpf = f"{cpf_clean[:3]}.{cpf_clean[3:6]}.{cpf_clean[6:9]}-{cpf_clean[9:]}"

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.cpf})"

    @property
    def cpf_clean(self):
        """Retorna CPF apenas com números"""
        return re.sub(r'[^0-9]', '', self.cpf) if self.cpf else ''

    @property
    def full_name(self):
        """Retorna nome completo"""
        return f"{self.first_name} {self.last_name}".strip()
