"""
Comando para criar tickets de exemplo.

Este comando cria assuntos e tickets de exemplo para demonstração
do sistema de suporte, seguindo boas práticas de dados de teste.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from support.models import Ticket, TicketSubject, TicketResponse

User = get_user_model()


class Command(BaseCommand):
    """Comando para criar dados de exemplo do sistema de suporte"""
    
    help = 'Cria tickets e assuntos de exemplo para teste'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cpf',
            type=str,
            help='CPF do cliente para criar tickets',
            default='111.444.777-35'
        )
        parser.add_argument(
            '--count',
            type=int,
            help='Número de tickets para criar',
            default=8
        )

    def handle(self, *args, **options):
        cpf = options['cpf']
        count = options['count']
        
        try:
            user = User.objects.get(cpf=cpf)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Usuário com CPF {cpf} não encontrado')
            )
            return
        
        # Cria assuntos se não existirem
        self.create_subjects()
        
        # Remove tickets existentes para evitar duplicatas
        Ticket.objects.filter(customer=user).delete()
        
        # Cria tickets de exemplo
        self.create_tickets(user, count)
        
        self.stdout.write(
            self.style.SUCCESS(f'Criados {count} tickets para {user.full_name}')
        )

    def create_subjects(self):
        """Cria assuntos de exemplo"""
        subjects_data = [
            {
                'ixc_subject_id': 1,
                'name': 'Problema de Conexão',
                'description': 'Problemas relacionados à conectividade de internet'
            },
            {
                'ixc_subject_id': 2,
                'name': 'Lentidão na Internet',
                'description': 'Velocidade abaixo do contratado'
            },
            {
                'ixc_subject_id': 3,
                'name': 'Instalação/Mudança',
                'description': 'Solicitações de instalação ou mudança de endereço'
            },
            {
                'ixc_subject_id': 4,
                'name': 'Financeiro',
                'description': 'Questões relacionadas a faturas e pagamentos'
            },
            {
                'ixc_subject_id': 5,
                'name': 'Suporte Técnico',
                'description': 'Suporte técnico geral e configurações'
            },
        ]
        
        for subject_data in subjects_data:
            subject, created = TicketSubject.objects.get_or_create(
                ixc_subject_id=subject_data['ixc_subject_id'],
                defaults={
                    'name': subject_data['name'],
                    'description': subject_data['description'],
                }
            )
            if created:
                self.stdout.write(f'Assunto criado: {subject.name}')

    def create_tickets(self, user, count):
        """Cria tickets de exemplo"""
        subjects = list(TicketSubject.objects.filter(is_active=True))
        
        tickets_data = [
            {
                'title': 'Internet sem conexão há 2 horas',
                'description': 'Desde às 14h de hoje minha internet parou de funcionar completamente. Já reiniciei o modem várias vezes mas não resolve.',
                'priority': 'alta',
                'status': 'aberto',
                'ticket_type': 'suporte',
            },
            {
                'title': 'Velocidade muito baixa',
                'description': 'Contratei 100MB mas estou recebendo apenas 20MB. Fiz teste em vários sites e sempre dá o mesmo resultado.',
                'priority': 'normal',
                'status': 'em_andamento',
                'ticket_type': 'suporte',
            },
            {
                'title': 'Solicitação de mudança de endereço',
                'description': 'Preciso transferir minha conexão para novo endereço. Quando posso agendar a instalação?',
                'priority': 'normal',
                'status': 'aguardando_cliente',
                'ticket_type': 'instalacao',
            },
            {
                'title': 'Dúvida sobre fatura',
                'description': 'Recebi uma cobrança extra na fatura deste mês. Gostaria de entender o que é.',
                'priority': 'baixa',
                'status': 'resolvido',
                'ticket_type': 'financeiro',
            },
            {
                'title': 'Configuração do Wi-Fi',
                'description': 'Preciso de ajuda para configurar uma nova senha do Wi-Fi. Como posso fazer isso?',
                'priority': 'baixa',
                'status': 'fechado',
                'ticket_type': 'suporte',
            },
            {
                'title': 'Instabilidade na conexão',
                'description': 'Internet fica caindo várias vezes por dia, principalmente à noite. Problema começou semana passada.',
                'priority': 'alta',
                'status': 'aberto',
                'ticket_type': 'suporte',
            },
            {
                'title': 'Upgrade de plano',
                'description': 'Gostaria de fazer upgrade do meu plano atual de 50MB para 200MB. Qual o procedimento?',
                'priority': 'normal',
                'status': 'em_andamento',
                'ticket_type': 'comercial',
            },
            {
                'title': 'Problema no roteador',
                'description': 'O roteador está com a luz vermelha piscando e não consigo conectar nenhum dispositivo.',
                'priority': 'urgente',
                'status': 'aberto',
                'ticket_type': 'suporte',
            },
        ]
        
        now = timezone.now()
        
        for i in range(min(count, len(tickets_data))):
            ticket_data = tickets_data[i]
            
            # Data de criação (últimos 30 dias)
            days_ago = random.randint(0, 30)
            created_at = now - timedelta(days=days_ago)
            
            # Seleciona assunto aleatório
            subject = random.choice(subjects)
            
            # Cria ticket
            ticket = Ticket.objects.create(
                ixc_ticket_id=2000 + i,
                customer=user,
                subject=subject,
                title=ticket_data['title'],
                description=ticket_data['description'],
                priority=ticket_data['priority'],
                status=ticket_data['status'],
                ticket_type=ticket_data['ticket_type'],
                created_at=created_at,
            )
            
            # Adiciona algumas respostas para tickets mais antigos
            if days_ago > 5 and random.choice([True, False]):
                self.create_responses(ticket, created_at)
            
            self.stdout.write(f'Ticket #{ticket.ixc_ticket_id} criado - {ticket.status}')

    def create_responses(self, ticket, ticket_created_at):
        """Cria respostas de exemplo para um ticket"""
        responses_data = [
            {
                'message': 'Obrigado por entrar em contato. Vamos verificar o problema e retornar em breve.',
                'response_type': 'tecnico',
                'author_name': 'João Silva - Suporte Técnico',
            },
            {
                'message': 'Consegui fazer alguns testes aqui. O problema parece estar relacionado ao equipamento.',
                'response_type': 'cliente',
                'author_name': ticket.customer.full_name,
            },
            {
                'message': 'Vamos agendar uma visita técnica para verificar o equipamento. Qual o melhor horário?',
                'response_type': 'tecnico',
                'author_name': 'Maria Santos - Suporte Técnico',
            },
        ]
        
        for i, response_data in enumerate(responses_data):
            if random.choice([True, False]):  # 50% chance de criar cada resposta
                response_time = ticket_created_at + timedelta(hours=random.randint(1, 48))
                
                TicketResponse.objects.create(
                    ticket=ticket,
                    message=response_data['message'],
                    response_type=response_data['response_type'],
                    author=ticket.customer if response_data['response_type'] == 'cliente' else None,
                    author_name=response_data['author_name'],
                    created_at=response_time,
                )
