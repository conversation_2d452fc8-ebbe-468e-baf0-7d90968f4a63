# Generated by Django 4.2.7 on 2025-06-28 14:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('support', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='ticket',
            name='affected_equipment',
            field=models.CharField(blank=True, help_text='Equipamentos ou serviços afetados', max_length=200),
        ),
        migrations.AddField(
            model_name='ticket',
            name='attempted_solutions',
            field=models.TextField(blank=True, help_text='Soluções já tentadas pelo cliente'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='browser_version',
            field=models.CharField(blank=True, help_text='Navegador e versão (para problemas web)', max_length=100),
        ),
        migrations.AddField(
            model_name='ticket',
            name='connection_type',
            field=models.CharField(blank=True, help_text='Tipo de conexão (fibra, cabo, wireless, etc.)', max_length=50),
        ),
        migrations.AddField(
            model_name='ticket',
            name='custom_subject',
            field=models.CharField(blank=True, help_text='Assunto personalizado sugerido pelo cliente', max_length=200),
        ),
        migrations.AddField(
            model_name='ticket',
            name='custom_subject_reason',
            field=models.TextField(blank=True, help_text='Justificativa para o assunto personalizado'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='error_message',
            field=models.TextField(blank=True, help_text='Mensagem de erro específica (se houver)'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='impact_description',
            field=models.TextField(blank=True, help_text='Descrição do impacto do problema'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='operating_system',
            field=models.CharField(blank=True, help_text='Sistema operacional utilizado', max_length=100),
        ),
        migrations.AddField(
            model_name='ticket',
            name='problem_occurred_at',
            field=models.DateTimeField(blank=True, help_text='Data e hora em que o problema ocorreu', null=True),
        ),
        migrations.AddField(
            model_name='ticket',
            name='steps_to_reproduce',
            field=models.TextField(blank=True, help_text='Passos para reproduzir o problema'),
        ),
    ]
