from django import forms
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth import authenticate
from django.core.validators import RegexValidator
import re


class CPFLoginForm(AuthenticationForm):
    """Formulário de login customizado para CPF"""
    
    username = forms.CharField(
        label='CPF',
        max_length=14,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite seu CPF',
            'autocomplete': 'username',
        }),
        validators=[
            RegexValidator(
                regex=r'^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$',
                message='CPF deve estar no formato XXX.XXX.XXX-XX ou apenas números'
            )
        ]
    )
    
    password = forms.CharField(
        label='Senha',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite sua senha',
            'autocomplete': 'current-password',
        })
    )
    
    def clean_username(self):
        """Limpa e valida o CPF"""
        cpf = self.cleaned_data.get('username')
        if cpf:
            # Remove formatação
            cpf_clean = re.sub(r'[^0-9]', '', cpf)

            # Valida se tem 11 dígitos
            if len(cpf_clean) != 11:
                raise forms.ValidationError('CPF deve ter 11 dígitos')

            # Retorna CPF como foi digitado (o backend vai tratar)
            return cpf
        return cpf
    
    def clean(self):
        """Validação customizada do formulário"""
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')
        
        if username and password:
            self.user_cache = authenticate(
                self.request,
                username=username,
                password=password
            )
            
            if self.user_cache is None:
                raise forms.ValidationError(
                    'CPF ou senha incorretos.',
                    code='invalid_login'
                )
            else:
                self.confirm_login_allowed(self.user_cache)
        
        return self.cleaned_data


class PasswordResetForm(forms.Form):
    """Formulário para recuperação de senha"""
    
    cpf = forms.CharField(
        label='CPF',
        max_length=14,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite seu CPF',
        }),
        validators=[
            RegexValidator(
                regex=r'^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$',
                message='CPF deve estar no formato XXX.XXX.XXX-XX ou apenas números'
            )
        ]
    )
    
    email = forms.EmailField(
        label='Email',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite seu email',
        })
    )
    
    def clean_cpf(self):
        """Limpa e valida o CPF"""
        cpf = self.cleaned_data.get('cpf')
        if cpf:
            # Remove formatação
            cpf_clean = re.sub(r'[^0-9]', '', cpf)
            
            # Valida se tem 11 dígitos
            if len(cpf_clean) != 11:
                raise forms.ValidationError('CPF deve ter 11 dígitos')
            
            return cpf_clean
        return cpf


class SetPasswordForm(forms.Form):
    """Formulário para definir senha na primeira autenticação"""
    
    password1 = forms.CharField(
        label='Nova Senha',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite sua nova senha',
        }),
        min_length=8,
        help_text='A senha deve ter pelo menos 8 caracteres.'
    )
    
    password2 = forms.CharField(
        label='Confirmar Senha',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirme sua nova senha',
        })
    )
    
    def clean_password2(self):
        """Valida se as senhas coincidem"""
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')
        
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError('As senhas não coincidem.')
        
        return password2
