#!/usr/bin/env python
"""
Script para testar configurações de deploy localmente
Execute: python test_deploy.py
"""

import os
import sys
import subprocess
from pathlib import Path

def check_file_exists(filename):
    """Verifica se um arquivo existe"""
    if Path(filename).exists():
        print(f"✅ {filename} existe")
        return True
    else:
        print(f"❌ {filename} não encontrado")
        return False

def check_requirements():
    """Verifica se todas as dependências estão no requirements.txt"""
    required_packages = [
        'gunicorn',
        'whitenoise', 
        'dj-database-url'
    ]
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read()
            
        missing = []
        for package in required_packages:
            if package not in content:
                missing.append(package)
        
        if missing:
            print(f"❌ Pacotes faltando no requirements.txt: {', '.join(missing)}")
            return False
        else:
            print("✅ Todas as dependências de deploy estão no requirements.txt")
            return True
            
    except FileNotFoundError:
        print("❌ requirements.txt não encontrado")
        return False

def test_collectstatic():
    """Testa a coleta de arquivos estáticos"""
    print("\n🧪 Testando coleta de arquivos estáticos...")
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'collectstatic', '--noinput', '--dry-run'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            print("✅ Collectstatic funcionando")
            return True
        else:
            print(f"❌ Erro no collectstatic: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Erro ao testar collectstatic: {e}")
        return False

def test_check():
    """Executa django check"""
    print("\n🧪 Executando Django check...")
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'check'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            print("✅ Django check passou")
            return True
        else:
            print(f"❌ Django check falhou: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Erro ao executar check: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 Testando configurações de deploy...\n")
    
    # Lista de verificações
    checks = []
    
    # Verificar arquivos necessários
    print("📁 Verificando arquivos necessários:")
    checks.append(check_file_exists('Procfile'))
    checks.append(check_file_exists('runtime.txt'))
    checks.append(check_file_exists('requirements.txt'))
    checks.append(check_file_exists('manage.py'))
    
    print("\n📦 Verificando dependências:")
    checks.append(check_requirements())
    
    # Testes do Django
    checks.append(test_check())
    checks.append(test_collectstatic())
    
    # Resultado final
    print("\n" + "="*50)
    if all(checks):
        print("🎉 SUCESSO! Projeto pronto para deploy!")
        print("\n📋 Próximos passos:")
        print("1. Fazer push para GitHub")
        print("2. Conectar no Railway: https://railway.app")
        print("3. Deploy automático!")
        print("\n📖 Veja deploy_railway.md para instruções detalhadas")
    else:
        print("❌ Alguns problemas encontrados. Corrija antes do deploy.")
        failed_count = len([c for c in checks if not c])
        print(f"   {failed_count} verificação(ões) falharam")
    
    print("="*50)

if __name__ == "__main__":
    main()
