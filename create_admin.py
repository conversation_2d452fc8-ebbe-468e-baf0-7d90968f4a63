#!/usr/bin/env python
"""
Script para criar usuário administrador automaticamente
Execute: python manage.py shell < create_admin.py
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'provedor.settings')
django.setup()

from django.contrib.auth import get_user_model
from authentication.models import Customer

User = get_user_model()

def create_admin_user():
    """Cria um usuário administrador padrão"""
    
    # Dados do admin
    cpf = '11144477735'  # CPF válido para teste
    email = '<EMAIL>'
    password = 'admin123'
    
    # Verificar se já existe
    if User.objects.filter(cpf=cpf).exists():
        print(f"✅ Usuário admin já existe (CPF: {cpf})")
        user = User.objects.get(cpf=cpf)
    else:
        # Criar usuário
        user = User.objects.create_user(
            cpf=cpf,
            email=email,
            password=password,
            first_name='Administrador',
            last_name='<PERSON><PERSON><PERSON>',
            is_staff=True,
            is_superuser=True,
            is_active=True
        )
        print(f"✅ Usuário admin criado!")
    
    print(f"""
🔑 CREDENCIAIS DE ACESSO:
   CPF: {cpf}
   Email: {email}
   Senha: {password}
   
🌐 URLs IMPORTANTES:
   Login: /auth/login/
   Admin: /admin/
   Dashboard: /dashboard/
   Novo Chamado: /suporte/novo/
   
⚠️  IMPORTANTE: Altere a senha após o primeiro login!
""")

if __name__ == "__main__":
    create_admin_user()
