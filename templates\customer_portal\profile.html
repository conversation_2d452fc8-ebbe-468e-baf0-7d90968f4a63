{% extends 'base.html' %}

{% block title %}Meu Perfil - Portal do Cliente{% endblock %}
{% block page_title %}Meu Perfil{% endblock %}

{% block extra_css %}
<style>
.profile-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.form-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.form-section h5 {
    color: var(--gray-800);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray-200);
}

.form-group {
    margin-bottom: 1rem;
}

.form-check {
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}
</style>
{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <div class="profile-avatar mx-auto">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <div class="col-md-10">
                <h2 class="mb-1">{{ user.first_name }} {{ user.last_name }}</h2>
                <p class="mb-1 opacity-75">{{ user.email }}</p>
                <p class="mb-0 opacity-75">
                    <i class="fas fa-id-card me-1"></i>CPF: {{ user.cpf }}
                </p>
            </div>
        </div>
    </div>
    
    <form method="post" class="needs-validation" novalidate>
        {% csrf_token %}
        
        <div class="row">
            <!-- Dados Pessoais -->
            <div class="col-lg-8">
                <div class="form-section">
                    <h5><i class="fas fa-user me-2"></i>Dados Pessoais</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    {{ form.first_name.label }}
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.first_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    {{ form.last_name.label }}
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.last_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.help_text %}
                                    <div class="form-text">{{ form.email.help_text }}</div>
                                {% endif %}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    {{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.help_text %}
                                    <div class="form-text">{{ form.phone.help_text }}</div>
                                {% endif %}
                                {% if form.phone.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.phone.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Endereço -->
                <div class="form-section">
                    <h5><i class="fas fa-map-marker-alt me-2"></i>Endereço</h5>
                    
                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}" class="form-label">
                            {{ form.address.label }}
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.address.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.city.id_for_label }}" class="form-label">
                                    {{ form.city.label }}
                                </label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.city.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="{{ form.state.id_for_label }}" class="form-label">
                                    {{ form.state.label }}
                                </label>
                                {{ form.state }}
                                {% if form.state.help_text %}
                                    <div class="form-text">{{ form.state.help_text }}</div>
                                {% endif %}
                                {% if form.state.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.state.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="{{ form.zip_code.id_for_label }}" class="form-label">
                                    {{ form.zip_code.label }}
                                </label>
                                {{ form.zip_code }}
                                {% if form.zip_code.help_text %}
                                    <div class="form-text">{{ form.zip_code.help_text }}</div>
                                {% endif %}
                                {% if form.zip_code.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.zip_code.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Preferências -->
                <div class="form-section">
                    <h5><i class="fas fa-cog me-2"></i>Preferências de Notificação</h5>
                    
                    <div class="form-check mb-3">
                        {{ form.receive_email_notifications }}
                        <label class="form-check-label" for="{{ form.receive_email_notifications.id_for_label }}">
                            <strong>{{ form.receive_email_notifications.label }}</strong>
                            <div class="text-muted small">Receba atualizações sobre faturas, suporte e novidades por e-mail</div>
                        </label>
                    </div>
                    
                    <div class="form-check">
                        {{ form.receive_push_notifications }}
                        <label class="form-check-label" for="{{ form.receive_push_notifications.id_for_label }}">
                            <strong>{{ form.receive_push_notifications.label }}</strong>
                            <div class="text-muted small">Receba notificações instantâneas no navegador</div>
                        </label>
                    </div>
                </div>
                
                <!-- Botões de Ação -->
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Salvar Alterações
                    </button>
                    <a href="{% url 'customer_portal:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </a>
                </div>
            </div>
            
            <!-- Sidebar com Ações -->
            <div class="col-lg-4">
                <div class="form-section">
                    <h5><i class="fas fa-shield-alt me-2"></i>Segurança</h5>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-2"></i>Alterar Senha
                        </button>
                        <a href="{% url 'authentication:logout' %}" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>Sair da Conta
                        </a>
                    </div>
                </div>
                
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>Informações da Conta</h5>
                    
                    <div class="small text-muted">
                        <div class="mb-2">
                            <strong>CPF:</strong> {{ user.cpf }}
                        </div>
                        <div class="mb-2">
                            <strong>Cliente desde:</strong> {{ user.date_joined|date:"d/m/Y" }}
                        </div>
                        <div class="mb-2">
                            <strong>Último acesso:</strong> {{ user.last_login|date:"d/m/Y H:i" }}
                        </div>
                        {% if user.ixc_customer_id %}
                        <div class="mb-2">
                            <strong>ID Cliente:</strong> {{ user.ixc_customer_id }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Modal para Alterar Senha -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>Alterar Senha
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Para alterar sua senha, você será redirecionado para uma página segura.</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Dica:</strong> Use uma senha forte com pelo menos 8 caracteres, incluindo letras, números e símbolos.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <a href="#" class="btn btn-warning">
                    <i class="fas fa-key me-2"></i>Alterar Senha
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Máscaras para campos
    const phoneInput = document.querySelector('[data-mask="(00) 00000-0000"]');
    const zipInput = document.querySelector('[data-mask="00000-000"]');
    
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                if (value.length > 6) {
                    value = value.replace(/(\d{2})(\d{5})(\d{0,4})/, '($1) $2-$3');
                } else if (value.length > 2) {
                    value = value.replace(/(\d{2})(\d{0,5})/, '($1) $2');
                } else if (value.length > 0) {
                    value = value.replace(/(\d{0,2})/, '($1');
                }
                e.target.value = value;
            }
        });
    }
    
    if (zipInput) {
        zipInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 8) {
                if (value.length > 5) {
                    value = value.replace(/(\d{5})(\d{0,3})/, '$1-$2');
                }
                e.target.value = value;
            }
        });
    }
    
    // Validação do formulário
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }
});
</script>
{% endblock %}
