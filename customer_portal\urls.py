"""
URLs para o portal do cliente.

Define as rotas principais do portal incluindo
dashboard, perfil do usuário e outras funcionalidades.
"""

from django.urls import path
from . import views

app_name = 'customer_portal'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('perfil/', views.UserProfileView.as_view(), name='profile'),
    path('busca/', views.global_search, name='search'),
]
