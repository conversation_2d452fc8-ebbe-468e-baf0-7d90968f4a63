/* Modern Minimalist CSS for Portal do Cliente */

/* CSS Variables */
:root {
    /* Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;

    /* Grays */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Layout */
    --sidebar-width: 280px;
    --topbar-height: 70px;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--gray-50);
    color: var(--gray-800);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: white;
    border-right: 1px solid var(--gray-200);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.sidebar-brand i {
    font-size: 1.5rem;
}

.brand-text {
    color: var(--gray-800);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.sidebar-menu {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.sidebar-menu .nav {
    padding: 0 1rem;
}

.sidebar-menu .nav-item {
    margin-bottom: 0.25rem;
}

.sidebar-menu .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
}

.sidebar-menu .nav-link:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.sidebar-menu .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar-menu .nav-link i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--gray-200);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--gray-800);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    font-size: 0.75rem;
    color: var(--gray-500);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-menu-toggle {
    background: none;
    border: none;
    color: var(--gray-400);
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.user-menu-toggle:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

/* Topbar Styles */
.topbar {
    position: fixed;
    top: 0;
    left: var(--sidebar-width);
    right: 0;
    height: var(--topbar-height);
    background: white;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 999;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.topbar-right {
    display: flex;
    align-items: center;
}

.topbar-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.topbar-actions .btn {
    background: none;
    border: none;
    color: var(--gray-500);
    padding: 0.75rem;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
    position: relative;
}

.topbar-actions .btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.notification-btn {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: var(--danger-color);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* Main Content Styles */
.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--topbar-height);
    min-height: calc(100vh - var(--topbar-height));
}

.content-wrapper {
    padding: 2rem;
}

.page-content {
    max-width: 100%;
}

/* Messages */
.messages-container {
    margin-bottom: 1.5rem;
}

.messages-container .alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
    box-shadow: var(--shadow-sm);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: transparent;
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    transition: all 0.2s ease;
    border: none;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-outline-primary {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-success {
    border: 1px solid var(--success-color);
    color: var(--success-color);
    background: transparent;
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-warning {
    border: 1px solid var(--warning-color);
    color: var(--warning-color);
    background: transparent;
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-info {
    border: 1px solid var(--info-color);
    color: var(--info-color);
    background: transparent;
}

.btn-outline-info:hover {
    background-color: var(--info-color);
    color: white;
    transform: translateY(-1px);
}

.btn-outline-danger {
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
    background: transparent;
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    color: white;
    transform: translateY(-1px);
}

/* Força bordas para todos os botões outline */
.btn[class*="btn-outline"] {
    border-width: 1px !important;
    border-style: solid !important;
}

/* CSS específico para ações rápidas */
.card-body .btn-outline-primary {
    border: 1px solid var(--primary-color) !important;
}

.card-body .btn-outline-success {
    border: 1px solid var(--success-color) !important;
}

.card-body .btn-outline-warning {
    border: 1px solid var(--warning-color) !important;
}

.card-body .btn-outline-info {
    border: 1px solid var(--info-color) !important;
}

/* Form Controls */
.form-control {
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-700);
    padding: 1rem;
    font-size: 0.875rem;
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-100);
    font-size: 0.875rem;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .topbar {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .content-wrapper {
        padding: 1rem;
    }

    .topbar {
        padding: 0 1rem;
    }

    .page-title h1 {
        font-size: 1.25rem;
    }
}

@media (max-width: 767.98px) {
    .content-wrapper {
        padding: 0.75rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8125rem;
    }
}

/* Utilities */
.text-muted {
    color: var(--gray-500) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Custom Scrollbar */
.sidebar-menu::-webkit-scrollbar {
    width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Focus States */
.btn:focus,
.form-control:focus {
    outline: none;
}

/* Dropdown Menus */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--gray-100);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* Status Indicators */
.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--danger-color);
}

.status-warning {
    color: var(--warning-color);
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Notification Dropdown */
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
}

.notification-item {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-100);
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: var(--gray-50);
}

.notification-item.unread {
    background-color: rgba(99, 102, 241, 0.05);
    border-left: 3px solid var(--primary-color);
}

.notification-icon {
    width: 24px;
    text-align: center;
    color: var(--gray-500);
}

.notification-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.notification-message {
    font-size: 0.8125rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.notification-dropdown .dropdown-header {
    padding: 1rem;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
}

/* Search Dropdown */
.search-dropdown {
    width: 400px;
    max-height: 500px;
    overflow-y: auto;
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
}

.search-dropdown .dropdown-header {
    padding: 1rem;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.search-results {
    max-height: 300px;
    overflow-y: auto;
}

.search-result-section {
    padding: 0.75rem 1rem 0.25rem;
    background-color: var(--gray-100);
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--gray-100);
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.search-result-item:hover {
    background-color: var(--gray-50);
    color: inherit;
    text-decoration: none;
}

.search-result-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.search-result-meta {
    font-size: 0.8125rem;
    color: var(--gray-600);
}

/* Print Styles */
@media print {
    .sidebar,
    .topbar {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
    }

    .content-wrapper {
        padding: 0 !important;
    }
}
}
