from django.shortcuts import render, redirect
from django.contrib.auth import login, logout
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import FormView
from django.utils import timezone
from .forms import CPFLoginForm, PasswordResetForm, SetPasswordForm
from .models import Customer


class LoginView(FormView):
    """View para login com CPF"""
    template_name = 'authentication/login.html'
    form_class = CPFLoginForm
    success_url = reverse_lazy('customer_portal:dashboard')

    def dispatch(self, request, *args, **kwargs):
        # Redireciona se já estiver logado
        if request.user.is_authenticated:
            return redirect(self.success_url)
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        """Processa login válido"""
        user = form.get_user()
        login(self.request, user)

        # Atualiza data do primeiro login se necessário
        if not user.first_login:
            user.first_login = timezone.now()
            user.save(update_fields=['first_login'])

        messages.success(self.request, f'Bem-vindo, {user.first_name}!')
        return super().form_valid(form)

    def form_invalid(self, form):
        """Processa login inválido"""
        messages.error(self.request, 'CPF ou senha incorretos.')
        return super().form_invalid(form)


def logout_view(request):
    """View para logout"""
    if request.user.is_authenticated:
        messages.info(request, 'Você foi desconectado com sucesso.')
    logout(request)
    return redirect('authentication:login')


class PasswordResetView(FormView):
    """View para recuperação de senha"""
    template_name = 'authentication/password_reset.html'
    form_class = PasswordResetForm
    success_url = reverse_lazy('authentication:password_reset_done')

    def form_valid(self, form):
        """Processa solicitação de recuperação de senha"""
        cpf = form.cleaned_data['cpf']
        email = form.cleaned_data['email']

        try:
            user = Customer.objects.get(cpf__icontains=cpf, email=email)
            # Aqui você implementaria o envio de email
            # Por enquanto, apenas mostra mensagem de sucesso
            messages.success(
                self.request,
                'Se os dados estiverem corretos, você receberá um email com instruções para redefinir sua senha.'
            )
        except Customer.DoesNotExist:
            # Não revela se o usuário existe ou não por segurança
            messages.success(
                self.request,
                'Se os dados estiverem corretos, você receberá um email com instruções para redefinir sua senha.'
            )

        return super().form_valid(form)


def password_reset_done(request):
    """View de confirmação de solicitação de recuperação"""
    return render(request, 'authentication/password_reset_done.html')


class SetPasswordView(FormView):
    """View para definir senha na primeira autenticação"""
    template_name = 'authentication/set_password.html'
    form_class = SetPasswordForm
    success_url = reverse_lazy('authentication:login')

    def form_valid(self, form):
        """Processa definição de nova senha"""
        # Aqui você implementaria a lógica para definir senha
        # baseada em token ou outro método de verificação
        password = form.cleaned_data['password1']

        messages.success(
            self.request,
            'Senha definida com sucesso! Faça login com suas credenciais.'
        )
        return super().form_valid(form)
