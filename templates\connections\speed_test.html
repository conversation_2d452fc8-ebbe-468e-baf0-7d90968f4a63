{% extends 'base.html' %}

{% block title %}Teste de Velocidade - Portal do Cliente{% endblock %}
{% block page_title %}Teste de Velocidade{% endblock %}

{% block extra_css %}
<style>
.speed-test-container {
    max-width: 800px;
    margin: 0 auto;
}

.speed-gauge {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto;
    background: conic-gradient(
        from 0deg,
        #ef4444 0deg 60deg,
        #f59e0b 60deg 120deg,
        #10b981 120deg 180deg,
        #3b82f6 180deg 240deg,
        #8b5cf6 240deg 300deg,
        #ef4444 300deg 360deg
    );
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.speed-gauge::before {
    content: '';
    position: absolute;
    width: 160px;
    height: 160px;
    background: white;
    border-radius: 50%;
}

.speed-value {
    position: relative;
    z-index: 2;
    text-align: center;
}

.speed-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
}

.speed-unit {
    font-size: 0.875rem;
    color: var(--gray-500);
    font-weight: 500;
}

.test-button {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.test-button:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
}

.test-button:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
}

.test-progress {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem 0;
}

.test-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    width: 0%;
    transition: width 0.3s ease;
}

.result-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
}

.result-metric {
    text-align: center;
    padding: 1rem;
}

.result-metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
}

.result-metric-label {
    font-size: 0.875rem;
    color: var(--gray-500);
    font-weight: 500;
}

.history-table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.quality-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.quality-excellent { background: var(--success-color); color: white; }
.quality-good { background: var(--info-color); color: white; }
.quality-fair { background: var(--warning-color); color: white; }
.quality-poor { background: var(--danger-color); color: white; }
</style>
{% endblock %}

{% block content %}
<div class="speed-test-container fade-in">
    <!-- Test Interface -->
    <div class="result-card text-center mb-4">
        <h3 class="mb-4">Teste sua Velocidade de Internet</h3>
        
        <!-- Speed Gauges -->
        <div class="row mb-4" id="speedGauges" style="display: none;">
            <div class="col-md-4">
                <div class="speed-gauge mb-3">
                    <div class="speed-value">
                        <div class="speed-number" id="downloadSpeed">0</div>
                        <div class="speed-unit">Mbps</div>
                    </div>
                </div>
                <h6>Download</h6>
            </div>
            <div class="col-md-4">
                <div class="speed-gauge mb-3">
                    <div class="speed-value">
                        <div class="speed-number" id="uploadSpeed">0</div>
                        <div class="speed-unit">Mbps</div>
                    </div>
                </div>
                <h6>Upload</h6>
            </div>
            <div class="col-md-4">
                <div class="speed-gauge mb-3">
                    <div class="speed-value">
                        <div class="speed-number" id="pingValue">0</div>
                        <div class="speed-unit">ms</div>
                    </div>
                </div>
                <h6>Ping</h6>
            </div>
        </div>
        
        <!-- Test Button -->
        <div class="mb-4">
            <button id="testButton" class="test-button">
                <i class="fas fa-play me-2"></i>
                Iniciar Teste
            </button>
        </div>
        
        <!-- Progress Bar -->
        <div id="testProgress" style="display: none;">
            <div class="test-progress">
                <div class="test-progress-bar" id="progressBar"></div>
            </div>
            <div id="testStatus" class="text-muted">Preparando teste...</div>
        </div>
        
        <!-- Results -->
        <div id="testResults" style="display: none;">
            <div class="row">
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="result-metric-value" id="finalDownload">0</div>
                        <div class="result-metric-label">Download (Mbps)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="result-metric-value" id="finalUpload">0</div>
                        <div class="result-metric-label">Upload (Mbps)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="result-metric-value" id="finalPing">0</div>
                        <div class="result-metric-label">Ping (ms)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="result-metric-value" id="qualityRating">-</div>
                        <div class="result-metric-label">Qualidade</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Plan Information -->
    {% if plan_details %}
    <div class="result-card mb-4">
        <h5><i class="fas fa-info-circle me-2"></i>Seu Plano</h5>
        <div class="row">
            <div class="col-md-6">
                <strong>Download Contratado:</strong> {{ plan_details.velocidade_download|default:"100" }} Mbps
            </div>
            <div class="col-md-6">
                <strong>Upload Contratado:</strong> {{ plan_details.velocidade_upload|default:"50" }} Mbps
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Statistics -->
    {% if test_stats %}
    <div class="result-card mb-4">
        <h5><i class="fas fa-chart-bar me-2"></i>Estatísticas dos Seus Testes</h5>
        <div class="row">
            <div class="col-md-4">
                <div class="result-metric">
                    <div class="result-metric-value">{{ test_stats.avg_download|floatformat:1 }}</div>
                    <div class="result-metric-label">Download Médio (Mbps)</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="result-metric">
                    <div class="result-metric-value">{{ test_stats.avg_upload|floatformat:1 }}</div>
                    <div class="result-metric-label">Upload Médio (Mbps)</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="result-metric">
                    <div class="result-metric-value">{{ test_stats.avg_ping|floatformat:1 }}</div>
                    <div class="result-metric-label">Ping Médio (ms)</div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Recent Tests -->
    {% if recent_tests %}
    <div class="history-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Data/Hora</th>
                        <th>Download</th>
                        <th>Upload</th>
                        <th>Ping</th>
                        <th>Qualidade</th>
                        <th>Servidor</th>
                    </tr>
                </thead>
                <tbody>
                    {% for test in recent_tests %}
                    <tr>
                        <td>{{ test.test_date|date:"d/m/Y H:i" }}</td>
                        <td>
                            <strong>{{ test.download_speed|floatformat:1 }} Mbps</strong>
                            <small class="text-muted d-block">{{ test.download_percentage|floatformat:0 }}% do plano</small>
                        </td>
                        <td>
                            <strong>{{ test.upload_speed|floatformat:1 }} Mbps</strong>
                            <small class="text-muted d-block">{{ test.upload_percentage|floatformat:0 }}% do plano</small>
                        </td>
                        <td>{{ test.ping|floatformat:1 }} ms</td>
                        <td>
                            <span class="quality-badge quality-{{ test.quality_rating|lower }}">
                                {{ test.get_quality_display }}
                            </span>
                        </td>
                        <td>{{ test.server_name }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="result-card text-center">
        <i class="fas fa-tachometer-alt fa-3x text-muted mb-3"></i>
        <h5>Nenhum teste realizado ainda</h5>
        <p class="text-muted">Clique no botão acima para fazer seu primeiro teste de velocidade!</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const testButton = document.getElementById('testButton');
    const testProgress = document.getElementById('testProgress');
    const testStatus = document.getElementById('testStatus');
    const progressBar = document.getElementById('progressBar');
    const speedGauges = document.getElementById('speedGauges');
    const testResults = document.getElementById('testResults');
    
    let isTestRunning = false;
    
    testButton.addEventListener('click', function() {
        if (isTestRunning) return;
        
        runSpeedTest();
    });
    
    function runSpeedTest() {
        isTestRunning = true;
        testButton.disabled = true;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testando...';
        
        // Mostrar elementos de progresso
        testProgress.style.display = 'block';
        speedGauges.style.display = 'flex';
        testResults.style.display = 'none';
        
        // Simular progresso
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            progressBar.style.width = progress + '%';
            
            if (progress < 30) {
                testStatus.textContent = 'Testando latência...';
            } else if (progress < 70) {
                testStatus.textContent = 'Testando velocidade de download...';
                updateGauge('downloadSpeed', Math.random() * 120);
            } else if (progress < 95) {
                testStatus.textContent = 'Testando velocidade de upload...';
                updateGauge('uploadSpeed', Math.random() * 60);
                updateGauge('pingValue', 20 + Math.random() * 40);
            } else {
                testStatus.textContent = 'Finalizando teste...';
            }
            
            if (progress >= 100) {
                clearInterval(progressInterval);
                executeTest();
            }
        }, 100);
    }
    
    function updateGauge(elementId, value) {
        document.getElementById(elementId).textContent = value.toFixed(1);
    }
    
    function executeTest() {
        fetch('/conexoes/api/run-speed-test/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayResults(data.results);
            } else {
                alert('Erro ao executar teste: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro de conexão. Tente novamente.');
        })
        .finally(() => {
            isTestRunning = false;
            testButton.disabled = false;
            testButton.innerHTML = '<i class="fas fa-redo me-2"></i>Testar Novamente';
            testProgress.style.display = 'none';
        });
    }
    
    function displayResults(results) {
        // Atualizar gauges finais
        updateGauge('downloadSpeed', results.download_speed);
        updateGauge('uploadSpeed', results.upload_speed);
        updateGauge('pingValue', results.ping);
        
        // Mostrar resultados detalhados
        document.getElementById('finalDownload').textContent = results.download_speed.toFixed(1);
        document.getElementById('finalUpload').textContent = results.upload_speed.toFixed(1);
        document.getElementById('finalPing').textContent = results.ping.toFixed(1);
        document.getElementById('qualityRating').textContent = results.quality_rating;
        
        testResults.style.display = 'block';
        
        // Recarregar página após 3 segundos para mostrar novo teste na tabela
        setTimeout(() => {
            location.reload();
        }, 3000);
    }
});
</script>
{% endblock %}
