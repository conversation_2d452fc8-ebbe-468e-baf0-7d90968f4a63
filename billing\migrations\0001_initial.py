# Generated by Django 4.2.7 on 2025-06-27 15:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ixc_invoice_id', models.IntegerField(help_text='ID da fatura na IXCSoft', unique=True)),
                ('description', models.CharField(help_text='Descrição da fatura', max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Valor da fatura', max_digits=10)),
                ('due_date', models.DateField(help_text='Data de vencimento')),
                ('issue_date', models.DateField(help_text='Data de emissão')),
                ('payment_date', models.DateField(blank=True, help_text='Data de pagamento', null=True)),
                ('status', models.Char<PERSON>ield(choices=[('aberto', 'Em Aberto'), ('pago', 'Pago'), ('vencido', 'Vencido'), ('cancelado', 'Cancelado')], default='aberto', max_length=20)),
                ('barcode', models.CharField(blank=True, help_text='Código de barras', max_length=100)),
                ('digitable_line', models.CharField(blank=True, help_text='Linha digitável', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Fatura',
                'verbose_name_plural': 'Faturas',
                'ordering': ['-due_date'],
            },
        ),
        migrations.CreateModel(
            name='PaymentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_paid', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_date', models.DateField()),
                ('payment_method', models.CharField(choices=[('boleto', 'Boleto Bancário'), ('pix', 'PIX'), ('cartao', 'Cartão de Crédito'), ('debito', 'Débito Automático'), ('dinheiro', 'Dinheiro'), ('outros', 'Outros')], max_length=20)),
                ('ixc_payment_id', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_history', to=settings.AUTH_USER_MODEL)),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='billing.invoice')),
            ],
            options={
                'verbose_name': 'Histórico de Pagamento',
                'verbose_name_plural': 'Histórico de Pagamentos',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=255)),
                ('quantity', models.DecimalField(decimal_places=2, default=1, max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='billing.invoice')),
            ],
            options={
                'verbose_name': 'Item da Fatura',
                'verbose_name_plural': 'Itens da Fatura',
            },
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['customer', 'status'], name='billing_inv_custome_775ec6_idx'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['due_date'], name='billing_inv_due_dat_e51895_idx'),
        ),
    ]
