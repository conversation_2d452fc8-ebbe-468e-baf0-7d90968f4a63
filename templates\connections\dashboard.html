{% extends 'base.html' %}

{% block title %}Conexões e Uso - Portal do Cliente{% endblock %}
{% block page_title %}Conexões{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-line me-2"></i>Conexões e Uso
            </h1>
            <p class="text-muted">Monitore seu uso de internet e qualidade da conexão</p>
        </div>
    </div>
    
    {% if error %}
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ error }}
            </div>
        </div>
    </div>
    {% else %}
    
    <!-- Informações do Plano -->
    {% if plan_details %}
    <div class="row mb-4">
        <div class="col">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="card-title mb-1">
                                <i class="fas fa-wifi me-2"></i>Plano Contratado
                            </h5>
                            <h3 class="mb-0">{{ plan_details.nome|default:"Plano Internet" }}</h3>
                            <p class="mb-0 opacity-75">
                                Download: {{ plan_details.velocidade_download|default:"100" }}MB | 
                                Upload: {{ plan_details.velocidade_upload|default:"50" }}MB
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-tachometer-alt fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Cards de Estatísticas -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Uso do Mês</h6>
                            <h4 class="mb-0">
                                {% if usage_summary.total_gb %}
                                    {{ usage_summary.total_gb|floatformat:1 }}GB
                                {% else %}
                                    0GB
                                {% endif %}
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-download fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Velocidade Média</h6>
                            <h4 class="mb-0">
                                {% if local_stats.speed_stats.avg_download %}
                                    {{ local_stats.speed_stats.avg_download|floatformat:0 }}MB
                                {% else %}
                                    --
                                {% endif %}
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-gauge-high fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Ping Médio</h6>
                            <h4 class="mb-0">
                                {% if local_stats.speed_stats.avg_ping %}
                                    {{ local_stats.speed_stats.avg_ping|floatformat:0 }}ms
                                {% else %}
                                    --
                                {% endif %}
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-stopwatch fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Testes Realizados</h6>
                            <h4 class="mb-0">
                                {{ local_stats.speed_stats.count|default:0 }}
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-vial fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Gráficos e Dados -->
    <div class="row mb-4">
        <!-- Gráfico de Uso -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-area me-2"></i>Uso de Internet - Últimos 7 Dias
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="usageChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Últimos Testes de Velocidade -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>Últimos Testes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_speed_tests %}
                        {% for test in recent_speed_tests %}
                        <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                            <div>
                                <div class="fw-bold">{{ test.velocidade_download|floatformat:0 }}MB</div>
                                <small class="text-muted">{{ test.data_teste|date:"d/m H:i" }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-success">{{ test.ping|floatformat:0 }}ms</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-vial fa-2x mb-2"></i>
                            <p class="mb-0">Nenhum teste realizado</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Resumo de Uso Detalhado -->
    {% if usage_summary %}
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Resumo de Uso do Mês
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <h6 class="text-muted">Total Baixado</h6>
                            <h4 class="text-primary">{{ usage_summary.download_gb|default:0|floatformat:1 }}GB</h4>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <h6 class="text-muted">Total Enviado</h6>
                            <h4 class="text-success">{{ usage_summary.upload_gb|default:0|floatformat:1 }}GB</h4>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <h6 class="text-muted">Pico de Velocidade</h6>
                            <h4 class="text-warning">{{ usage_summary.pico_velocidade|default:0|floatformat:0 }}MB</h4>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <h6 class="text-muted">Tempo Online</h6>
                            <h4 class="text-info">{{ usage_summary.tempo_online|default:"0h" }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Ações Rápidas -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Ações Rápidas
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'connections:speed_test' %}" class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-tachometer-alt fa-2x d-block mb-2"></i>
                                Teste de Velocidade
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-history fa-2x d-block mb-2"></i>
                                Histórico Completo
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'connections:reports' %}" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-file-alt fa-2x d-block mb-2"></i>
                                Relatório Mensal
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'support:ticket_create' %}" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-headset fa-2x d-block mb-2"></i>
                                Reportar Problema
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Gráfico de uso de internet
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('usageChart').getContext('2d');
    
    // Dados simulados para demonstração
    const usageData = {
        labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
        datasets: [{
            label: 'Download (GB)',
            data: [2.5, 3.2, 1.8, 4.1, 3.7, 5.2, 4.8],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2,
            fill: true
        }, {
            label: 'Upload (GB)',
            data: [0.5, 0.8, 0.3, 0.9, 0.7, 1.1, 0.9],
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 2,
            fill: true
        }]
    };
    
    new Chart(ctx, {
        type: 'line',
        data: usageData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Gigabytes (GB)'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            }
        }
    });
});

// Teste de velocidade agora tem página própria
</script>
{% endblock %}
