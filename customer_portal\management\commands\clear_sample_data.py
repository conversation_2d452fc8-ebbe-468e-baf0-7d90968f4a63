"""
Comando para limpar dados de exemplo do sistema.

<PERSON><PERSON> comando remove todos os dados fictícios criados para demonstração,
mantendo apenas a estrutura do banco de dados e superusers.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction

from billing.models import Invoice, InvoiceItem, PaymentHistory
from support.models import Ticket, TicketSubject, TicketResponse, TicketAttachment
from connections.models import ConnectionSession, SpeedTest, BandwidthUsage

User = get_user_model()


class Command(BaseCommand):
    """Comando para limpar dados de exemplo"""
    
    help = 'Remove todos os dados de exemplo, mantendo apenas superusers'

    def add_arguments(self, parser):
        parser.add_argument(
            '--keep-superusers',
            action='store_true',
            help='Manter superusers no sistema (padrão: True)',
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirma a operação sem prompt interativo',
        )

    def handle(self, *args, **options):
        keep_superusers = options.get('keep_superusers', True)
        confirm = options.get('confirm', False)
        
        if not confirm:
            response = input(
                'Esta operação irá remover TODOS os dados de exemplo.\n'
                'Tem certeza que deseja continuar? (digite "sim" para confirmar): '
            )
            if response.lower() != 'sim':
                self.stdout.write(self.style.WARNING('Operação cancelada.'))
                return
        
        try:
            with transaction.atomic():
                self.clear_all_data(keep_superusers)
                
            self.stdout.write(
                self.style.SUCCESS('✅ Dados de exemplo removidos com sucesso!')
            )
            
            if keep_superusers:
                remaining_users = User.objects.filter(is_superuser=True).count()
                self.stdout.write(
                    self.style.SUCCESS(f'✅ {remaining_users} superuser(s) mantido(s)')
                )
            
            self.stdout.write(
                self.style.WARNING(
                    '\n💡 Para criar dados de exemplo novamente, use:\n'
                    '   python manage.py create_sample_invoices\n'
                    '   python manage.py create_sample_tickets\n'
                    '   python manage.py create_sample_connections'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Erro ao limpar dados: {e}')
            )

    def clear_all_data(self, keep_superusers=True):
        """Remove todos os dados de exemplo"""
        
        # Contadores para relatório
        counts = {}
        
        # Remover dados de billing
        counts['invoices'] = Invoice.objects.count()
        counts['invoice_items'] = InvoiceItem.objects.count()
        counts['payments'] = PaymentHistory.objects.count()
        
        Invoice.objects.all().delete()
        InvoiceItem.objects.all().delete()
        PaymentHistory.objects.all().delete()
        
        self.stdout.write('✅ Dados de faturas removidos')
        
        # Remover dados de support
        counts['ticket_responses'] = TicketResponse.objects.count()
        counts['ticket_attachments'] = TicketAttachment.objects.count()
        counts['tickets'] = Ticket.objects.count()
        counts['ticket_subjects'] = TicketSubject.objects.count()
        
        TicketResponse.objects.all().delete()
        TicketAttachment.objects.all().delete()
        Ticket.objects.all().delete()
        TicketSubject.objects.all().delete()
        
        self.stdout.write('✅ Dados de suporte removidos')
        
        # Remover dados de connections
        counts['sessions'] = ConnectionSession.objects.count()
        counts['speed_tests'] = SpeedTest.objects.count()
        counts['bandwidth_usage'] = BandwidthUsage.objects.count()
        
        ConnectionSession.objects.all().delete()
        SpeedTest.objects.all().delete()
        BandwidthUsage.objects.all().delete()
        
        self.stdout.write('✅ Dados de conexões removidos')
        
        # Remover usuários (exceto superusers se solicitado)
        if keep_superusers:
            counts['users'] = User.objects.filter(is_superuser=False).count()
            User.objects.filter(is_superuser=False).delete()
            self.stdout.write('✅ Usuários de teste removidos (superusers mantidos)')
        else:
            counts['users'] = User.objects.count()
            User.objects.all().delete()
            self.stdout.write('✅ Todos os usuários removidos')
        
        # Relatório final
        self.stdout.write('\n📊 Resumo da limpeza:')
        self.stdout.write(f'   • {counts["invoices"]} faturas')
        self.stdout.write(f'   • {counts["invoice_items"]} itens de fatura')
        self.stdout.write(f'   • {counts["payments"]} pagamentos')
        self.stdout.write(f'   • {counts["tickets"]} tickets')
        self.stdout.write(f'   • {counts["ticket_responses"]} respostas')
        self.stdout.write(f'   • {counts["ticket_attachments"]} anexos')
        self.stdout.write(f'   • {counts["ticket_subjects"]} assuntos')
        self.stdout.write(f'   • {counts["sessions"]} sessões de conexão')
        self.stdout.write(f'   • {counts["speed_tests"]} testes de velocidade')
        self.stdout.write(f'   • {counts["bandwidth_usage"]} registros de uso')
        self.stdout.write(f'   • {counts["users"]} usuários')
        
        total_records = sum(counts.values())
        self.stdout.write(f'\n🎯 Total: {total_records} registros removidos')
