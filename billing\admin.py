from django.contrib import admin
from .models import Invoice, InvoiceItem, PaymentHistory


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('ixc_invoice_id', 'customer', 'description', 'amount', 'due_date', 'status')
    list_filter = ('status', 'due_date', 'created_at')
    search_fields = ('ixc_invoice_id', 'customer__cpf', 'customer__first_name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'due_date'


@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    list_display = ('invoice', 'description', 'quantity', 'unit_price', 'total_price')
    list_filter = ('invoice__status',)
    search_fields = ('description', 'invoice__ixc_invoice_id')


@admin.register(PaymentHistory)
class PaymentHistoryAdmin(admin.ModelAdmin):
    list_display = ('customer', 'invoice', 'amount_paid', 'payment_date', 'payment_method')
    list_filter = ('payment_method', 'payment_date')
    search_fields = ('customer__cpf', 'invoice__ixc_invoice_id')
    date_hierarchy = 'payment_date'
