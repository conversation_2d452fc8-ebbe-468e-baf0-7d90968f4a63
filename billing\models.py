from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class Invoice(models.Model):
    """Modelo para armazenar cache de faturas da IXCSoft"""

    STATUS_CHOICES = [
        ('aberto', '<PERSON> Aberto'),
        ('pago', 'Pago'),
        ('vencido', 'Vencido'),
        ('cancelado', 'Cancelado'),
    ]

    # Dados da IXCSoft
    ixc_invoice_id = models.IntegerField(unique=True, help_text='ID da fatura na IXCSoft')
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='invoices')

    # Dados da fatura
    description = models.CharField(max_length=255, help_text='Descrição da fatura')
    amount = models.DecimalField(max_digits=10, decimal_places=2, help_text='Valor da fatura')
    due_date = models.DateField(help_text='Data de vencimento')
    issue_date = models.DateField(help_text='Data de emissão')
    payment_date = models.DateField(null=True, blank=True, help_text='Data de pagamento')

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='aberto')

    # Dados adicionais
    barcode = models.CharField(max_length=100, blank=True, help_text='Código de barras')
    digitable_line = models.CharField(max_length=100, blank=True, help_text='Linha digitável')

    # Controle de cache
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Fatura'
        verbose_name_plural = 'Faturas'
        ordering = ['-due_date']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['due_date']),
        ]

    def __str__(self):
        return f"Fatura {self.ixc_invoice_id} - {self.customer.full_name} - R$ {self.amount}"

    @property
    def is_overdue(self):
        """Verifica se a fatura está vencida"""
        from django.utils import timezone
        return self.status == 'aberto' and self.due_date < timezone.now().date()

    @property
    def days_until_due(self):
        """Calcula dias até o vencimento"""
        from django.utils import timezone
        if self.status != 'aberto':
            return None

        today = timezone.now().date()
        delta = self.due_date - today
        return delta.days

    @property
    def formatted_amount(self):
        """Retorna valor formatado em reais"""
        return f"R$ {self.amount:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')


class InvoiceItem(models.Model):
    """Itens da fatura"""

    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    description = models.CharField(max_length=255)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = 'Item da Fatura'
        verbose_name_plural = 'Itens da Fatura'

    def __str__(self):
        return f"{self.description} - R$ {self.total_price}"

    def save(self, *args, **kwargs):
        # Calcula o total automaticamente
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)


class PaymentHistory(models.Model):
    """Histórico de pagamentos"""

    PAYMENT_METHOD_CHOICES = [
        ('boleto', 'Boleto Bancário'),
        ('pix', 'PIX'),
        ('cartao', 'Cartão de Crédito'),
        ('debito', 'Débito Automático'),
        ('dinheiro', 'Dinheiro'),
        ('outros', 'Outros'),
    ]

    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_history')
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='payments')

    # Dados do pagamento
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2)
    payment_date = models.DateField()
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)

    # Dados da IXCSoft
    ixc_payment_id = models.IntegerField(null=True, blank=True)

    # Controle
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Histórico de Pagamento'
        verbose_name_plural = 'Histórico de Pagamentos'
        ordering = ['-payment_date']

    def __str__(self):
        return f"Pagamento {self.amount_paid} - {self.payment_date}"
