{% extends 'base.html' %}

{% block title %}Meus Chamados - Portal do Cliente{% endblock %}
{% block page_title %}Suporte{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-0">
                <i class="fas fa-headset me-2"></i>Meus Chamados
            </h1>
            <p class="text-muted">Gerencie seus tickets de suporte</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'support:ticket_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Abrir Novo Chamado
            </a>
        </div>
    </div>
    
    <!-- Estatísticas -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total</h6>
                            <h4 class="mb-0">{{ stats.total }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ticket-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Em Aberto</h6>
                            <h4 class="mb-0">{{ stats.open }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Aguardando</h6>
                            <h4 class="mb-0">{{ stats.waiting }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Resolvidos</h6>
                            <h4 class="mb-0">{{ stats.resolved }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filtros e Busca -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-2">
                            <label for="{{ filter_form.status.id_for_label }}" class="form-label">Status</label>
                            {{ filter_form.status }}
                        </div>
                        <div class="col-md-2">
                            <label for="{{ filter_form.priority.id_for_label }}" class="form-label">Prioridade</label>
                            {{ filter_form.priority }}
                        </div>
                        <div class="col-md-2">
                            <label for="{{ filter_form.ticket_type.id_for_label }}" class="form-label">Tipo</label>
                            {{ filter_form.ticket_type }}
                        </div>
                        <div class="col-md-2">
                            <label for="{{ filter_form.date_from.id_for_label }}" class="form-label">De</label>
                            {{ filter_form.date_from }}
                        </div>
                        <div class="col-md-2">
                            <label for="{{ filter_form.date_to.id_for_label }}" class="form-label">Até</label>
                            {{ filter_form.date_to }}
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i>Filtrar
                            </button>
                        </div>
                    </form>
                    
                    <!-- Busca -->
                    <hr>
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <label for="{{ search_form.query.id_for_label }}" class="form-label">Buscar</label>
                            {{ search_form.query }}
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Buscar
                            </button>
                            <a href="{% url 'support:ticket_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Limpar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Lista de Tickets -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Lista de Chamados
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if tickets %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Chamado</th>
                                    <th>Assunto</th>
                                    <th>Título</th>
                                    <th>Status</th>
                                    <th>Prioridade</th>
                                    <th>Criado em</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ticket in tickets %}
                                <tr>
                                    <td>
                                        <strong>#{{ ticket.ixc_ticket_id }}</strong>
                                        {% if ticket.days_open > 7 %}
                                            <br><small class="text-warning">
                                                <i class="fas fa-clock"></i>
                                                {{ ticket.days_open }} dias
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>{{ ticket.subject.name }}</td>
                                    <td>
                                        <strong>{{ ticket.title|truncatechars:40 }}</strong>
                                        {% if ticket.responses.count > 0 %}
                                            <br><small class="text-muted">
                                                <i class="fas fa-comments"></i>
                                                {{ ticket.responses.count }} resposta{{ ticket.responses.count|pluralize }}
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge {{ ticket.status_badge_class }}">
                                            {{ ticket.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ ticket.priority_badge_class }}">
                                            {{ ticket.get_priority_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ ticket.created_at|date:"d/m/Y H:i" }}
                                        <br><small class="text-muted">{{ ticket.created_at|timesince }} atrás</small>
                                    </td>
                                    <td>
                                        <a href="{% url 'support:ticket_detail' ticket.pk %}" 
                                           class="btn btn-outline-primary btn-sm" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Paginação -->
                    {% if page_obj.has_other_pages %}
                    <div class="card-footer">
                        <nav aria-label="Navegação de páginas">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-headset fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum chamado encontrado</h5>
                        <p class="text-muted">Você ainda não possui chamados de suporte.</p>
                        <a href="{% url 'support:ticket_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Abrir Primeiro Chamado
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
