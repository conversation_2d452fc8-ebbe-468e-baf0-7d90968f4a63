from django.contrib.auth.backends import BaseBackend
from django.contrib.auth import get_user_model
import re

User = get_user_model()


class CPFBackend(BaseBackend):
    """
    Backend de autenticação customizado para login com CPF
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        if username is None or password is None:
            return None
        
        try:
            # Limpa o CPF (remove formatação)
            cpf_clean = re.sub(r'[^0-9]', '', username)

            # Tenta buscar pelo CPF formatado primeiro
            try:
                user = User.objects.get(cpf=username)
            except User.DoesNotExist:
                # Se não encontrar, busca pelo CPF limpo
                formatted_cpf = f"{cpf_clean[:3]}.{cpf_clean[3:6]}.{cpf_clean[6:9]}-{cpf_clean[9:]}"
                user = User.objects.get(cpf=formatted_cpf)

            # Verifica a senha
            if user.check_password(password):
                return user
                
        except User.DoesNotExist:
            # Executa hash da senha mesmo se usuário não existir
            # para evitar timing attacks
            User().set_password(password)
            return None
        
        return None
    
    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
