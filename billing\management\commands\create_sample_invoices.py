from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
import random

from billing.models import Invoice, InvoiceItem

User = get_user_model()


class Command(BaseCommand):
    help = 'Cria faturas de exemplo para teste'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cpf',
            type=str,
            help='CPF do cliente para criar faturas',
            default='111.444.777-35'
        )
        parser.add_argument(
            '--count',
            type=int,
            help='Número de faturas para criar',
            default=12
        )

    def handle(self, *args, **options):
        cpf = options['cpf']
        count = options['count']
        
        try:
            user = User.objects.get(cpf=cpf)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Usuário com CPF {cpf} não encontrado')
            )
            return
        
        # Define ID do cliente na IXCSoft (simulado)
        if not user.ixc_customer_id:
            user.ixc_customer_id = 12345
            user.save()
        
        # Remove faturas existentes para evitar duplicatas
        Invoice.objects.filter(customer=user).delete()
        
        # Cria faturas de exemplo
        today = timezone.now().date()
        
        for i in range(count):
            # Calcula data de vencimento (faturas dos últimos 12 meses)
            months_ago = count - i - 1
            due_date = today - timedelta(days=30 * months_ago)
            issue_date = due_date - timedelta(days=5)
            
            # Define status baseado na data
            if due_date < today - timedelta(days=30):
                status = 'pago'
                payment_date = due_date + timedelta(days=random.randint(1, 10))
            elif due_date < today:
                status = random.choice(['pago', 'vencido'])
                payment_date = due_date + timedelta(days=random.randint(1, 5)) if status == 'pago' else None
            else:
                status = 'aberto'
                payment_date = None
            
            # Valor da fatura
            base_amount = Decimal('89.90')  # Plano básico
            amount = base_amount + Decimal(random.uniform(-10, 20))
            
            # Cria fatura
            invoice = Invoice.objects.create(
                ixc_invoice_id=1000 + i,
                customer=user,
                description=f'Mensalidade Internet - {due_date.strftime("%m/%Y")}',
                amount=amount,
                due_date=due_date,
                issue_date=issue_date,
                payment_date=payment_date,
                status=status,
                barcode=f'23791234567890123456789012345678901234567{i:02d}',
                digitable_line=f'23791.23456 78901.234567 89012.345678 9 12345678901234567{i:02d}'
            )
            
            # Cria itens da fatura
            InvoiceItem.objects.create(
                invoice=invoice,
                description='Plano Internet 100MB',
                quantity=1,
                unit_price=Decimal('79.90'),
                total_price=Decimal('79.90')
            )
            
            if random.choice([True, False]):
                InvoiceItem.objects.create(
                    invoice=invoice,
                    description='Taxa de instalação',
                    quantity=1,
                    unit_price=Decimal('10.00'),
                    total_price=Decimal('10.00')
                )
            
            self.stdout.write(f'Fatura #{invoice.ixc_invoice_id} criada - {status}')
        
        self.stdout.write(
            self.style.SUCCESS(f'Criadas {count} faturas para {user.full_name}')
        )
