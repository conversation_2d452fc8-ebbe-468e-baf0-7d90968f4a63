# Generated by Django 4.2.7 on 2025-06-28 12:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Título da notificação', max_length=200)),
                ('message', models.TextField(help_text='Mensagem da notificação')),
                ('type', models.CharField(choices=[('info', 'Informação'), ('success', 'Sucesso'), ('warning', 'Aviso'), ('error', 'Erro')], default='info', help_text='Tipo da notificação', max_length=10)),
                ('category', models.Char<PERSON>ield(choices=[('billing', 'Faturas'), ('support', 'Suporte'), ('connection', 'Conexão'), ('system', 'Sistema'), ('account', 'Conta')], default='system', help_text='Categoria da notificação', max_length=20)),
                ('is_read', models.BooleanField(default=False, help_text='Se a notificação foi lida')),
                ('url', models.URLField(blank=True, help_text='URL para ação relacionada (opcional)')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Data de criação')),
                ('read_at', models.DateTimeField(blank=True, help_text='Data de leitura', null=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Data de expiração (opcional)', null=True)),
                ('user', models.ForeignKey(help_text='Usuário que receberá a notificação', on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notificação',
                'verbose_name_plural': 'Notificações',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_read'], name='customer_po_user_id_0155ae_idx'), models.Index(fields=['created_at'], name='customer_po_created_a36359_idx'), models.Index(fields=['category'], name='customer_po_categor_1ee3af_idx')],
            },
        ),
    ]
