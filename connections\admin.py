"""
Admin para o sistema de conexões e uso de internet.

Configurações do Django Admin para gerenciamento
de sessões, testes de velocidade e uso de banda.
"""

from django.contrib import admin
from .models import ConnectionSession, SpeedTest, BandwidthUsage


@admin.register(ConnectionSession)
class ConnectionSessionAdmin(admin.ModelAdmin):
    """Admin para sessões de conexão"""
    list_display = (
        'ixc_session_id', 'customer', 'start_time', 'duration_formatted',
        'total_mb', 'status'
    )
    list_filter = ('status', 'start_time', 'created_at')
    search_fields = (
        'ixc_session_id', 'customer__cpf', 'customer__first_name',
        'ip_address', 'mac_address'
    )
    readonly_fields = (
        'created_at', 'updated_at', 'total_bytes', 'total_mb',
        'total_gb', 'duration_formatted', 'average_speed_mbps'
    )
    date_hierarchy = 'start_time'

    fieldsets = (
        ('Informações Básicas', {
            'fields': ('ixc_session_id', 'customer', 'status')
        }),
        ('Per<PERSON><PERSON> da Sessão', {
            'fields': ('start_time', 'end_time', 'duration_seconds', 'duration_formatted')
        }),
        ('Dados de Rede', {
            'fields': ('ip_address', 'mac_address', 'nas_ip')
        }),
        ('Tráfego', {
            'fields': (
                'bytes_uploaded', 'bytes_downloaded', 'total_bytes',
                'packets_uploaded', 'packets_downloaded'
            )
        }),
        ('Estatísticas', {
            'fields': ('total_mb', 'total_gb', 'average_speed_mbps')
        }),
        ('Controle', {
            'fields': ('termination_cause', 'created_at', 'updated_at')
        }),
    )


@admin.register(SpeedTest)
class SpeedTestAdmin(admin.ModelAdmin):
    """Admin para testes de velocidade"""
    list_display = (
        'customer', 'test_date', 'download_speed', 'upload_speed',
        'ping', 'quality_rating'
    )
    list_filter = ('test_date', 'server_location', 'created_at')
    search_fields = (
        'customer__cpf', 'customer__first_name', 'server_name',
        'ixc_test_id'
    )
    readonly_fields = (
        'created_at', 'download_percentage', 'upload_percentage',
        'quality_rating', 'quality_badge_class'
    )
    date_hierarchy = 'test_date'

    fieldsets = (
        ('Informações Básicas', {
            'fields': ('ixc_test_id', 'customer', 'test_date')
        }),
        ('Servidor de Teste', {
            'fields': ('server_name', 'server_location')
        }),
        ('Resultados', {
            'fields': (
                'download_speed', 'upload_speed', 'ping', 'jitter'
            )
        }),
        ('Plano Contratado', {
            'fields': ('contracted_download', 'contracted_upload')
        }),
        ('Análise', {
            'fields': (
                'download_percentage', 'upload_percentage',
                'quality_rating', 'quality_badge_class'
            )
        }),
        ('Dados Técnicos', {
            'fields': ('ip_address', 'user_agent')
        }),
        ('Controle', {
            'fields': ('created_at',)
        }),
    )


@admin.register(BandwidthUsage)
class BandwidthUsageAdmin(admin.ModelAdmin):
    """Admin para uso de banda"""
    list_display = (
        'customer', 'period_type', 'period_start', 'total_gb',
        'peak_speed_mbps'
    )
    list_filter = ('period_type', 'period_start', 'created_at')
    search_fields = ('customer__cpf', 'customer__first_name')
    readonly_fields = (
        'created_at', 'updated_at', 'total_bytes', 'total_gb',
        'peak_speed_mbps', 'avg_speed_mbps'
    )
    date_hierarchy = 'period_start'

    fieldsets = (
        ('Informações Básicas', {
            'fields': ('customer', 'period_type')
        }),
        ('Período', {
            'fields': ('period_start', 'period_end')
        }),
        ('Uso de Dados', {
            'fields': (
                'bytes_uploaded', 'bytes_downloaded', 'total_bytes', 'total_gb'
            )
        }),
        ('Velocidades', {
            'fields': (
                'peak_upload_speed', 'peak_download_speed', 'peak_speed_mbps',
                'avg_upload_speed', 'avg_download_speed', 'avg_speed_mbps'
            )
        }),
        ('Controle', {
            'fields': ('created_at', 'updated_at')
        }),
    )
