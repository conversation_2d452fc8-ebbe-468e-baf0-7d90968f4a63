# Generated by Django 4.2.7 on 2025-06-27 15:38

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ixc_ticket_id', models.IntegerField(help_text='ID do ticket na IXCSoft', unique=True)),
                ('title', models.CharField(help_text='Título resumido do ticket', max_length=200)),
                ('description', models.TextField(help_text='Descrição detalhada do problema', validators=[django.core.validators.MinLengthValidator(10)])),
                ('priority', models.CharField(choices=[('baixa', 'Baixa'), ('normal', 'Normal'), ('alta', 'Alta'), ('urgente', 'Urgente')], default='normal', help_text='Prioridade do ticket', max_length=20)),
                ('status', models.CharField(choices=[('aberto', 'Aberto'), ('em_andamento', 'Em Andamento'), ('aguardando_cliente', 'Aguardando Cliente'), ('resolvido', 'Resolvido'), ('fechado', 'Fechado'), ('cancelado', 'Cancelado')], default='aberto', help_text='Status atual do ticket', max_length=20)),
                ('ticket_type', models.CharField(choices=[('suporte', 'Suporte Técnico'), ('comercial', 'Comercial'), ('financeiro', 'Financeiro'), ('instalacao', 'Instalação'), ('manutencao', 'Manutenção')], default='suporte', help_text='Tipo do ticket', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Data de criação do ticket')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Data da última atualização')),
                ('resolved_at', models.DateTimeField(blank=True, help_text='Data de resolução do ticket', null=True)),
                ('closed_at', models.DateTimeField(blank=True, help_text='Data de fechamento do ticket', null=True)),
                ('assigned_technician', models.CharField(blank=True, help_text='Técnico responsável', max_length=100)),
                ('estimated_resolution', models.DateTimeField(blank=True, help_text='Previsão de resolução', null=True)),
                ('customer', models.ForeignKey(help_text='Cliente que abriu o ticket', on_delete=django.db.models.deletion.CASCADE, related_name='support_tickets', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Ticket de Suporte',
                'verbose_name_plural': 'Tickets de Suporte',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TicketSubject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ixc_subject_id', models.IntegerField(help_text='ID do assunto na IXCSoft', unique=True)),
                ('name', models.CharField(help_text='Nome do assunto', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Descrição detalhada do assunto')),
                ('is_active', models.BooleanField(default=True, help_text='Assunto ativo para seleção')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Assunto do Ticket',
                'verbose_name_plural': 'Assuntos dos Tickets',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['is_active'], name='support_tic_is_acti_0b52f2_idx')],
            },
        ),
        migrations.CreateModel(
            name='TicketResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(help_text='Conteúdo da resposta', validators=[django.core.validators.MinLengthValidator(5)])),
                ('response_type', models.CharField(choices=[('cliente', 'Cliente'), ('tecnico', 'Técnico'), ('sistema', 'Sistema')], default='cliente', help_text='Tipo de resposta', max_length=20)),
                ('author_name', models.CharField(help_text='Nome do autor (para técnicos externos)', max_length=100)),
                ('ixc_response_id', models.IntegerField(blank=True, help_text='ID da resposta na IXCSoft', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Data de criação da resposta')),
                ('is_internal', models.BooleanField(default=False, help_text='Resposta interna (não visível ao cliente)')),
                ('is_solution', models.BooleanField(default=False, help_text='Esta resposta resolve o ticket')),
                ('author', models.ForeignKey(blank=True, help_text='Autor da resposta', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ticket_responses', to=settings.AUTH_USER_MODEL)),
                ('ticket', models.ForeignKey(help_text='Ticket relacionado', on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='support.ticket')),
            ],
            options={
                'verbose_name': 'Resposta do Ticket',
                'verbose_name_plural': 'Respostas dos Tickets',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='TicketAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(help_text='Arquivo anexado', upload_to='support/attachments/%Y/%m/')),
                ('original_filename', models.CharField(help_text='Nome original do arquivo', max_length=255)),
                ('file_size', models.PositiveIntegerField(help_text='Tamanho do arquivo em bytes')),
                ('content_type', models.CharField(help_text='Tipo MIME do arquivo', max_length=100)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, help_text='Data do upload')),
                ('response', models.ForeignKey(blank=True, help_text='Resposta relacionada (opcional)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='support.ticketresponse')),
                ('ticket', models.ForeignKey(help_text='Ticket relacionado', on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='support.ticket')),
                ('uploaded_by', models.ForeignKey(help_text='Usuário que fez upload', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Anexo do Ticket',
                'verbose_name_plural': 'Anexos dos Tickets',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.AddField(
            model_name='ticket',
            name='subject',
            field=models.ForeignKey(help_text='Assunto do ticket', on_delete=django.db.models.deletion.PROTECT, related_name='tickets', to='support.ticketsubject'),
        ),
        migrations.AddIndex(
            model_name='ticketresponse',
            index=models.Index(fields=['ticket', 'created_at'], name='support_tic_ticket__9566a2_idx'),
        ),
        migrations.AddIndex(
            model_name='ticketresponse',
            index=models.Index(fields=['response_type'], name='support_tic_respons_d9c595_idx'),
        ),
        migrations.AddIndex(
            model_name='ticket',
            index=models.Index(fields=['customer', 'status'], name='support_tic_custome_476799_idx'),
        ),
        migrations.AddIndex(
            model_name='ticket',
            index=models.Index(fields=['status', 'priority'], name='support_tic_status_a0fe0d_idx'),
        ),
        migrations.AddIndex(
            model_name='ticket',
            index=models.Index(fields=['created_at'], name='support_tic_created_438cd5_idx'),
        ),
    ]
