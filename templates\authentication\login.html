{% extends 'base.html' %}

{% block title %}Login - Portal do Cliente{% endblock %}

{% block login_content %}
<div class="card shadow-lg border-0 bg-white">
    <div class="card-body p-5">
        <div class="text-center mb-4">
            <div class="d-inline-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle mb-3"
                 style="width: 80px; height: 80px;">
                <i class="fas fa-wifi fa-2x text-primary"></i>
            </div>
            <h3 class="fw-bold mb-2">Portal do Cliente</h3>
            <p class="text-muted mb-0">Acesse sua conta com seu CPF</p>
        </div>

        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}

            <div class="mb-4">
                <label for="{{ form.username.id_for_label }}" class="form-label fw-medium">
                    <i class="fas fa-id-card me-2"></i>CPF
                </label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="text-danger small mt-2">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        {{ form.username.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.password.id_for_label }}" class="form-label fw-medium">
                    <i class="fas fa-lock me-2"></i>Senha
                </label>
                {{ form.password }}
                {% if form.password.errors %}
                    <div class="text-danger small mt-2">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        {{ form.password.errors.0 }}
                    </div>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger border-0 bg-danger bg-opacity-10 text-danger mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ form.non_field_errors.0 }}
                </div>
            {% endif %}

            <div class="d-grid mb-4">
                <button type="submit" class="btn btn-primary btn-lg py-3 fw-medium">
                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                </button>
            </div>

            <div class="text-center">
                <a href="{% url 'authentication:password_reset' %}" class="text-decoration-none text-muted">
                    <i class="fas fa-key me-1"></i>Esqueceu sua senha?
                </a>
            </div>
        </form>

        <div class="text-center mt-4 pt-4 border-top">
            <p class="text-muted small mb-0">
                <i class="fas fa-shield-alt me-1"></i>
                Seus dados estão protegidos e seguros
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Máscara para CPF
document.addEventListener('DOMContentLoaded', function() {
    const cpfInput = document.getElementById('{{ form.username.id_for_label }}');
    
    cpfInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        
        if (value.length <= 11) {
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
        }
        
        e.target.value = value;
    });
});
</script>
{% endblock %}
