"""
Modelos para o sistema de suporte e chamados.

Este módulo contém os modelos que representam tickets de suporte,
respostas e assuntos, mantendo cache local dos dados da API IXCSoft.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinLengthValidator
from django.utils import timezone

User = get_user_model()


class TicketSubject(models.Model):
    """
    Assuntos disponíveis para tickets de suporte.

    Mantém cache local dos assuntos da IXCSoft para melhor performance
    e disponibilidade offline.
    """

    ixc_subject_id = models.IntegerField(
        unique=True,
        help_text='ID do assunto na IXCSoft'
    )
    name = models.CharField(
        max_length=100,
        help_text='Nome do assunto'
    )
    description = models.TextField(
        blank=True,
        help_text='Descrição detalhada do assunto'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Assunto ativo para seleção'
    )

    # Controle de cache
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Assunto do Ticket'
        verbose_name_plural = 'Assuntos dos Tickets'
        ordering = ['name']
        indexes = [
            models.Index(fields=['is_active']),
        ]

    def __str__(self) -> str:
        return self.name


class Ticket(models.Model):
    """
    Ticket de suporte do cliente.

    Representa uma ordem de serviço (OSS) da IXCSoft,
    mantendo cache local para melhor experiência do usuário.
    """

    PRIORITY_CHOICES = [
        ('baixa', 'Baixa'),
        ('normal', 'Normal'),
        ('alta', 'Alta'),
        ('urgente', 'Urgente'),
    ]

    STATUS_CHOICES = [
        ('aberto', 'Aberto'),
        ('em_andamento', 'Em Andamento'),
        ('aguardando_cliente', 'Aguardando Cliente'),
        ('resolvido', 'Resolvido'),
        ('fechado', 'Fechado'),
        ('cancelado', 'Cancelado'),
    ]

    TYPE_CHOICES = [
        ('suporte', 'Suporte Técnico'),
        ('comercial', 'Comercial'),
        ('financeiro', 'Financeiro'),
        ('instalacao', 'Instalação'),
        ('manutencao', 'Manutenção'),
    ]

    # Dados da IXCSoft
    ixc_ticket_id = models.IntegerField(
        unique=True,
        help_text='ID do ticket na IXCSoft'
    )
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='support_tickets',
        help_text='Cliente que abriu o ticket'
    )

    # Dados do ticket
    subject = models.ForeignKey(
        TicketSubject,
        on_delete=models.PROTECT,
        related_name='tickets',
        help_text='Assunto do ticket'
    )
    title = models.CharField(
        max_length=200,
        help_text='Título resumido do ticket'
    )
    description = models.TextField(
        validators=[MinLengthValidator(10)],
        help_text='Descrição detalhada do problema'
    )

    # Classificação
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='normal',
        help_text='Prioridade do ticket'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='aberto',
        help_text='Status atual do ticket'
    )
    ticket_type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        default='suporte',
        help_text='Tipo do ticket'
    )

    # Datas importantes
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text='Data de criação do ticket'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text='Data da última atualização'
    )
    resolved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Data de resolução do ticket'
    )
    closed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Data de fechamento do ticket'
    )

    # Dados adicionais
    assigned_technician = models.CharField(
        max_length=100,
        blank=True,
        help_text='Técnico responsável'
    )
    estimated_resolution = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Previsão de resolução'
    )

    # Informações detalhadas do problema
    problem_occurred_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Data e hora em que o problema ocorreu'
    )
    affected_equipment = models.CharField(
        max_length=200,
        blank=True,
        help_text='Equipamentos ou serviços afetados'
    )
    error_message = models.TextField(
        blank=True,
        help_text='Mensagem de erro específica (se houver)'
    )
    steps_to_reproduce = models.TextField(
        blank=True,
        help_text='Passos para reproduzir o problema'
    )
    attempted_solutions = models.TextField(
        blank=True,
        help_text='Soluções já tentadas pelo cliente'
    )
    impact_description = models.TextField(
        blank=True,
        help_text='Descrição do impacto do problema'
    )

    # Informações técnicas
    operating_system = models.CharField(
        max_length=100,
        blank=True,
        help_text='Sistema operacional utilizado'
    )
    browser_version = models.CharField(
        max_length=100,
        blank=True,
        help_text='Navegador e versão (para problemas web)'
    )
    connection_type = models.CharField(
        max_length=50,
        blank=True,
        help_text='Tipo de conexão (fibra, cabo, wireless, etc.)'
    )

    # Assunto personalizado
    custom_subject = models.CharField(
        max_length=200,
        blank=True,
        help_text='Assunto personalizado sugerido pelo cliente'
    )
    custom_subject_reason = models.TextField(
        blank=True,
        help_text='Justificativa para o assunto personalizado'
    )

    class Meta:
        verbose_name = 'Ticket de Suporte'
        verbose_name_plural = 'Tickets de Suporte'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self) -> str:
        return f"Ticket #{self.ixc_ticket_id} - {self.title}"

    @property
    def is_open(self) -> bool:
        """Verifica se o ticket está aberto"""
        return self.status in ['aberto', 'em_andamento', 'aguardando_cliente']

    @property
    def days_open(self) -> int:
        """Calcula há quantos dias o ticket está aberto"""
        if self.closed_at:
            return (self.closed_at.date() - self.created_at.date()).days
        return (timezone.now().date() - self.created_at.date()).days

    @property
    def priority_badge_class(self) -> str:
        """Retorna classe CSS para badge de prioridade"""
        priority_classes = {
            'baixa': 'bg-secondary',
            'normal': 'bg-primary',
            'alta': 'bg-warning',
            'urgente': 'bg-danger',
        }
        return priority_classes.get(self.priority, 'bg-primary')

    @property
    def status_badge_class(self) -> str:
        """Retorna classe CSS para badge de status"""
        status_classes = {
            'aberto': 'bg-warning',
            'em_andamento': 'bg-info',
            'aguardando_cliente': 'bg-secondary',
            'resolvido': 'bg-success',
            'fechado': 'bg-dark',
            'cancelado': 'bg-danger',
        }
        return status_classes.get(self.status, 'bg-primary')

    def can_add_response(self, user) -> bool:
        """Verifica se o usuário pode adicionar resposta"""
        return (
            self.customer == user and
            self.is_open and
            self.status != 'aguardando_cliente'
        )

    def mark_as_resolved(self) -> None:
        """Marca ticket como resolvido"""
        if self.status != 'resolvido':
            self.status = 'resolvido'
            self.resolved_at = timezone.now()
            self.save(update_fields=['status', 'resolved_at', 'updated_at'])


class TicketResponse(models.Model):
    """
    Resposta/Interação em um ticket de suporte.

    Representa as mensagens trocadas entre cliente e suporte
    durante o atendimento de um ticket.
    """

    RESPONSE_TYPE_CHOICES = [
        ('cliente', 'Cliente'),
        ('tecnico', 'Técnico'),
        ('sistema', 'Sistema'),
    ]

    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name='responses',
        help_text='Ticket relacionado'
    )

    # Dados da resposta
    message = models.TextField(
        validators=[MinLengthValidator(5)],
        help_text='Conteúdo da resposta'
    )
    response_type = models.CharField(
        max_length=20,
        choices=RESPONSE_TYPE_CHOICES,
        default='cliente',
        help_text='Tipo de resposta'
    )

    # Autor (pode ser nulo para respostas do sistema)
    author = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='ticket_responses',
        help_text='Autor da resposta'
    )
    author_name = models.CharField(
        max_length=100,
        help_text='Nome do autor (para técnicos externos)'
    )

    # Dados da IXCSoft
    ixc_response_id = models.IntegerField(
        null=True,
        blank=True,
        help_text='ID da resposta na IXCSoft'
    )

    # Controle temporal
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text='Data de criação da resposta'
    )

    # Flags de controle
    is_internal = models.BooleanField(
        default=False,
        help_text='Resposta interna (não visível ao cliente)'
    )
    is_solution = models.BooleanField(
        default=False,
        help_text='Esta resposta resolve o ticket'
    )

    class Meta:
        verbose_name = 'Resposta do Ticket'
        verbose_name_plural = 'Respostas dos Tickets'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['ticket', 'created_at']),
            models.Index(fields=['response_type']),
        ]

    def __str__(self) -> str:
        return f"Resposta de {self.author_name} em {self.created_at.strftime('%d/%m/%Y %H:%M')}"

    @property
    def is_from_customer(self) -> bool:
        """Verifica se a resposta é do cliente"""
        return self.response_type == 'cliente'

    @property
    def response_type_badge_class(self) -> str:
        """Retorna classe CSS para badge do tipo de resposta"""
        type_classes = {
            'cliente': 'bg-primary',
            'tecnico': 'bg-success',
            'sistema': 'bg-secondary',
        }
        return type_classes.get(self.response_type, 'bg-primary')

    def save(self, *args, **kwargs):
        """Override save para definir author_name automaticamente"""
        if not self.author_name and self.author:
            self.author_name = self.author.get_full_name() or self.author.email

        super().save(*args, **kwargs)

        # Atualiza timestamp do ticket pai
        self.ticket.save(update_fields=['updated_at'])


class TicketAttachment(models.Model):
    """
    Anexo de um ticket de suporte.

    Permite que clientes anexem arquivos aos tickets
    para melhor descrição do problema.
    """

    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name='attachments',
        help_text='Ticket relacionado'
    )
    response = models.ForeignKey(
        TicketResponse,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='attachments',
        help_text='Resposta relacionada (opcional)'
    )

    # Dados do arquivo
    file = models.FileField(
        upload_to='support/attachments/%Y/%m/',
        help_text='Arquivo anexado'
    )
    original_filename = models.CharField(
        max_length=255,
        help_text='Nome original do arquivo'
    )
    file_size = models.PositiveIntegerField(
        help_text='Tamanho do arquivo em bytes'
    )
    content_type = models.CharField(
        max_length=100,
        help_text='Tipo MIME do arquivo'
    )

    # Controle
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        help_text='Usuário que fez upload'
    )
    uploaded_at = models.DateTimeField(
        auto_now_add=True,
        help_text='Data do upload'
    )

    class Meta:
        verbose_name = 'Anexo do Ticket'
        verbose_name_plural = 'Anexos dos Tickets'
        ordering = ['-uploaded_at']

    def __str__(self) -> str:
        return f"Anexo: {self.original_filename}"

    @property
    def file_size_formatted(self) -> str:
        """Retorna tamanho do arquivo formatado"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} MB"

    @property
    def is_image(self) -> bool:
        """Verifica se o arquivo é uma imagem"""
        return self.content_type.startswith('image/')

    def save(self, *args, **kwargs):
        """Override save para definir metadados do arquivo"""
        if self.file and not self.original_filename:
            self.original_filename = self.file.name
            self.file_size = self.file.size

        super().save(*args, **kwargs)
