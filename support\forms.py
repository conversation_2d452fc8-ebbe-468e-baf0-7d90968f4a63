"""
Formulários para o sistema de suporte.

Este módulo contém formulários para criação e interação com tickets,
seguindo as melhores práticas de validação e UX.
"""

from django import forms
from django.core.validators import FileExtensionValidator
from django.core.exceptions import ValidationError

from .models import Ticket, TicketResponse, TicketSubject, TicketAttachment


class TicketCreateForm(forms.ModelForm):
    """
    Formulário para criação de novos tickets de suporte.

    Inclui validações customizadas e widgets otimizados
    para melhor experiência do usuário.
    """

    # Campo para upload de arquivos (será tratado via JavaScript)
    attachments = forms.CharField(
        required=False,
        widget=forms.HiddenInput(),
        label='Anexos'
    )

    # Checkbox para usar assunto personalizado
    use_custom_subject = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
        }),
        label='Meu problema não se encaixa nos assuntos disponíveis'
    )

    class Meta:
        model = Ticket
        fields = [
            'subject', 'use_custom_subject', 'custom_subject', 'custom_subject_reason',
            'title', 'description', 'priority', 'ticket_type',
            'problem_occurred_at', 'affected_equipment', 'error_message',
            'steps_to_reproduce', 'attempted_solutions', 'impact_description',
            'operating_system', 'browser_version', 'connection_type'
        ]
        widgets = {
            'subject': forms.Select(attrs={
                'class': 'form-select',
            }),
            'custom_subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Digite o assunto que melhor descreve seu problema',
                'maxlength': 200,
            }),
            'custom_subject_reason': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Explique por que nenhum dos assuntos existentes se adequa...',
                'rows': 3,
            }),
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Resumo do problema em poucas palavras',
                'maxlength': 200,
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Descreva detalhadamente o problema...',
                'rows': 6,
                'minlength': 10,
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select',
            }),
            'ticket_type': forms.Select(attrs={
                'class': 'form-select',
            }),
            'problem_occurred_at': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local',
            }),
            'affected_equipment': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Roteador, computador, telefone, etc.',
            }),
            'error_message': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Cole aqui a mensagem de erro exata (se houver)',
                'rows': 3,
            }),
            'steps_to_reproduce': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': '1. Primeiro eu fiz...\n2. Depois cliquei em...\n3. Então apareceu...',
                'rows': 4,
            }),
            'attempted_solutions': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Descreva o que você já tentou fazer para resolver...',
                'rows': 3,
            }),
            'impact_description': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Como este problema está afetando seu trabalho/uso?',
                'rows': 3,
            }),
            'operating_system': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Windows 11, macOS, Android, iOS',
            }),
            'browser_version': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Chrome 120, Firefox 115, Safari 17',
            }),
            'connection_type': forms.Select(attrs={
                'class': 'form-select',
            }, choices=[
                ('', 'Selecione o tipo de conexão'),
                ('fibra', 'Fibra Óptica'),
                ('cabo', 'Cabo/Coaxial'),
                ('wireless', 'Wireless/Wi-Fi'),
                ('movel', 'Dados Móveis'),
                ('outro', 'Outro'),
            ]),
        }
        labels = {
            'subject': 'Assunto',
            'custom_subject': 'Assunto Personalizado',
            'custom_subject_reason': 'Por que este assunto é necessário?',
            'title': 'Título do Chamado',
            'description': 'Descrição Detalhada',
            'priority': 'Prioridade',
            'ticket_type': 'Tipo de Atendimento',
            'problem_occurred_at': 'Quando o problema ocorreu?',
            'affected_equipment': 'Equipamentos Afetados',
            'error_message': 'Mensagem de Erro',
            'steps_to_reproduce': 'Como Reproduzir o Problema',
            'attempted_solutions': 'Soluções Já Tentadas',
            'impact_description': 'Impacto do Problema',
            'operating_system': 'Sistema Operacional',
            'browser_version': 'Navegador e Versão',
            'connection_type': 'Tipo de Conexão',
        }
        help_texts = {
            'title': 'Título claro e objetivo do problema',
            'description': 'Descreva o problema com o máximo de detalhes possível',
            'priority': 'Selecione a urgência do problema',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Filtra apenas assuntos ativos
        self.fields['subject'].queryset = TicketSubject.objects.filter(is_active=True)

        # Torna campos opcionais baseado na lógica de negócio
        optional_fields = [
            'custom_subject', 'custom_subject_reason', 'problem_occurred_at',
            'affected_equipment', 'error_message', 'steps_to_reproduce',
            'attempted_solutions', 'impact_description', 'operating_system',
            'browser_version', 'connection_type', 'attachments'
        ]

        for field_name in optional_fields:
            if field_name in self.fields:
                self.fields[field_name].required = False

        # Torna o campo subject não obrigatório inicialmente (será validado no clean)
        self.fields['subject'].required = False

    def clean(self):
        """Validações customizadas do formulário"""
        cleaned_data = super().clean()
        use_custom_subject = cleaned_data.get('use_custom_subject')
        subject = cleaned_data.get('subject')
        custom_subject = cleaned_data.get('custom_subject')
        custom_subject_reason = cleaned_data.get('custom_subject_reason')

        # Validar assunto personalizado
        if use_custom_subject:
            if not custom_subject:
                self.add_error('custom_subject', 'Este campo é obrigatório quando você marca a opção de assunto personalizado.')
            if not custom_subject_reason:
                self.add_error('custom_subject_reason', 'Por favor, explique por que precisa de um assunto personalizado.')
        else:
            if not subject:
                self.add_error('subject', 'Selecione um assunto ou marque a opção de assunto personalizado.')

        return cleaned_data

    def clean_custom_subject(self):
        """Valida o assunto personalizado"""
        custom_subject = self.cleaned_data.get('custom_subject', '').strip()
        use_custom_subject = self.cleaned_data.get('use_custom_subject', False)

        if use_custom_subject and custom_subject:
            if len(custom_subject) < 5:
                raise ValidationError('O assunto personalizado deve ter pelo menos 5 caracteres.')
            if len(custom_subject) > 200:
                raise ValidationError('O assunto personalizado não pode ter mais de 200 caracteres.')

        return custom_subject

    def clean_custom_subject_reason(self):
        """Valida a justificativa do assunto personalizado"""
        reason = self.cleaned_data.get('custom_subject_reason', '').strip()
        use_custom_subject = self.cleaned_data.get('use_custom_subject', False)

        if use_custom_subject and reason:
            if len(reason) < 10:
                raise ValidationError('A justificativa deve ter pelo menos 10 caracteres.')

        return reason

    def clean_title(self) -> str:
        """Valida e limpa o título do ticket"""
        title = self.cleaned_data.get('title', '').strip()
        
        if len(title) < 5:
            raise ValidationError('O título deve ter pelo menos 5 caracteres.')
        
        # Remove caracteres especiais desnecessários
        title = ' '.join(title.split())
        
        return title
    
    def clean_description(self) -> str:
        """Valida e limpa a descrição do ticket"""
        description = self.cleaned_data.get('description', '').strip()
        
        if len(description) < 10:
            raise ValidationError('A descrição deve ter pelo menos 10 caracteres.')
        
        # Verifica se não é apenas repetição do título
        title = self.cleaned_data.get('title', '')
        if title and description.lower() == title.lower():
            raise ValidationError('A descrição deve ser diferente do título.')
        
        return description


class TicketResponseForm(forms.ModelForm):
    """
    Formulário para adicionar respostas aos tickets.
    
    Permite que clientes respondam aos tickets de suporte
    com validações apropriadas.
    """
    
    class Meta:
        model = TicketResponse
        fields = ['message']
        widgets = {
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Digite sua mensagem...',
                'rows': 4,
                'minlength': 5,
            }),
        }
        labels = {
            'message': 'Sua Resposta',
        }
        help_texts = {
            'message': 'Adicione informações adicionais ou responda às perguntas do suporte',
        }
    
    def clean_message(self) -> str:
        """Valida e limpa a mensagem da resposta"""
        message = self.cleaned_data.get('message', '').strip()
        
        if len(message) < 5:
            raise ValidationError('A mensagem deve ter pelo menos 5 caracteres.')
        
        return message


class TicketAttachmentForm(forms.ModelForm):
    """
    Formulário para upload de anexos em tickets.

    Inclui validações de tipo e tamanho de arquivo
    para segurança e performance.
    """

    ALLOWED_EXTENSIONS = [
        'jpg', 'jpeg', 'png', 'gif', 'bmp',  # Imagens
        'pdf', 'doc', 'docx', 'txt', 'rtf',  # Documentos
        'zip', 'rar', '7z',                   # Arquivos compactados
    ]

    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

    class Meta:
        model = TicketAttachment
        fields = ['file']
        widgets = {
            'file': forms.FileInput(attrs={
                'class': 'form-control',
            }),
        }
        labels = {
            'file': 'Arquivo',
        }
        help_texts = {
            'file': 'Tipos permitidos: jpg, png, pdf, doc, txt, zip. Tamanho máximo: 5MB',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Configura widget com extensões permitidas
        self.fields['file'].widget.attrs.update({
            'accept': ','.join([f'.{ext}' for ext in self.ALLOWED_EXTENSIONS]),
        })

        # Adiciona validador de extensão
        self.fields['file'].validators.append(
            FileExtensionValidator(allowed_extensions=self.ALLOWED_EXTENSIONS)
        )
    
    def clean_file(self):
        """Valida o arquivo enviado"""
        file = self.cleaned_data.get('file')
        
        if not file:
            return file
        
        # Valida tamanho do arquivo
        if file.size > self.MAX_FILE_SIZE:
            raise ValidationError(
                f'O arquivo é muito grande. Tamanho máximo permitido: '
                f'{self.MAX_FILE_SIZE / (1024 * 1024):.0f}MB'
            )
        
        # Valida tipo MIME básico
        if not file.content_type:
            raise ValidationError('Não foi possível determinar o tipo do arquivo.')
        
        return file


class TicketFilterForm(forms.Form):
    """
    Formulário para filtrar tickets na listagem.
    
    Permite filtrar por status, prioridade, tipo e período
    para facilitar a navegação do cliente.
    """
    
    STATUS_CHOICES = [('', 'Todos os Status')] + Ticket.STATUS_CHOICES
    PRIORITY_CHOICES = [('', 'Todas as Prioridades')] + Ticket.PRIORITY_CHOICES
    TYPE_CHOICES = [('', 'Todos os Tipos')] + Ticket.TYPE_CHOICES
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    priority = forms.ChoiceField(
        choices=PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    ticket_type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
        }),
        label='Data Inicial'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
        }),
        label='Data Final'
    )
    
    def clean(self):
        """Valida o período de datas"""
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to:
            if date_from > date_to:
                raise ValidationError('A data inicial deve ser anterior à data final.')
            
            # Limita período máximo de consulta (performance)
            if (date_to - date_from).days > 365:
                raise ValidationError('O período máximo de consulta é de 1 ano.')
        
        return cleaned_data


class TicketSearchForm(forms.Form):
    """
    Formulário para busca textual em tickets.
    
    Permite buscar por título, descrição ou ID do ticket.
    """
    
    query = forms.CharField(
        max_length=100,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Buscar por título, descrição ou #ID...',
        }),
        label='Buscar'
    )
    
    def clean_query(self) -> str:
        """Valida e limpa a query de busca"""
        query = self.cleaned_data.get('query', '').strip()
        
        if len(query) < 3:
            raise ValidationError('A busca deve ter pelo menos 3 caracteres.')
        
        return query
