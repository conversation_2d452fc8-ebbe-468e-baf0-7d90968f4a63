import requests
import logging
from django.conf import settings
from django.core.cache import cache
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)


class IXCSoftAPIError(Exception):
    """Exceção customizada para erros da API IXCSoft"""
    pass


class IXCSoftAPIClient:
    """Cliente para comunicação com a API da IXCSoft"""
    
    def __init__(self):
        self.base_url = settings.IXCSOFT_API_URL.rstrip('/')
        self.token = settings.IXCSOFT_API_TOKEN
        self.session = requests.Session()
        
        # Headers padrão
        self.session.headers.update({
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """Faz requisição para a API com tratamento de erros"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            
            # Log da requisição (sem dados sensíveis)
            logger.info(f"IXCSoft API {method} {endpoint} - Status: {response.status_code}")
            
            return response.json() if response.content else {}
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"IXCSoft API HTTP Error: {e} - Response: {e.response.text}")
            raise IXCSoftAPIError(f"Erro HTTP {e.response.status_code}: {e.response.text}")
        
        except requests.exceptions.RequestException as e:
            logger.error(f"IXCSoft API Request Error: {e}")
            raise IXCSoftAPIError(f"Erro de conexão: {str(e)}")
        
        except json.JSONDecodeError as e:
            logger.error(f"IXCSoft API JSON Error: {e}")
            raise IXCSoftAPIError("Erro ao decodificar resposta da API")
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[Any, Any]:
        """GET request"""
        return self._make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[Any, Any]:
        """POST request"""
        return self._make_request('POST', endpoint, json=data)
    
    def put(self, endpoint: str, data: Optional[Dict] = None) -> Dict[Any, Any]:
        """PUT request"""
        return self._make_request('PUT', endpoint, json=data)
    
    def delete(self, endpoint: str) -> Dict[Any, Any]:
        """DELETE request"""
        return self._make_request('DELETE', endpoint)


class CustomerService:
    """Serviço para operações relacionadas a clientes"""
    
    def __init__(self):
        self.api = IXCSoftAPIClient()
    
    def get_customer_by_cpf(self, cpf: str) -> Optional[Dict]:
        """Busca cliente por CPF na API IXCSoft"""
        cache_key = f"ixc_customer_{cpf}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            # Endpoint para buscar cliente por CPF
            response = self.api.get('cliente', params={'cpf': cpf})
            
            if response and 'registros' in response:
                customer_data = response['registros'][0] if response['registros'] else None
                
                # Cache por 15 minutos
                if customer_data:
                    cache.set(cache_key, customer_data, 900)
                
                return customer_data
                
        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar cliente por CPF {cpf}: {e}")
            
        return None
    
    def get_customer_by_id(self, customer_id: int) -> Optional[Dict]:
        """Busca cliente por ID na API IXCSoft"""
        cache_key = f"ixc_customer_id_{customer_id}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            response = self.api.get(f'cliente/{customer_id}')
            
            if response:
                # Cache por 15 minutos
                cache.set(cache_key, response, 900)
                return response
                
        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar cliente por ID {customer_id}: {e}")
            
        return None


class BillingService:
    """Serviço para operações de cobrança e faturas"""
    
    def __init__(self):
        self.api = IXCSoftAPIClient()
    
    def get_customer_invoices(self, customer_id: int, limit: int = 12) -> List[Dict]:
        """Busca faturas do cliente"""
        cache_key = f"ixc_invoices_{customer_id}_{limit}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            params = {
                'cliente_id': customer_id,
                'limit': limit,
                'order': 'vencimento DESC'
            }
            
            response = self.api.get('fn_areceber', params=params)
            
            invoices = []
            if response and 'registros' in response:
                invoices = response['registros']
                
                # Cache por 5 minutos
                cache.set(cache_key, invoices, 300)
            
            return invoices
            
        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar faturas do cliente {customer_id}: {e}")
            return []
    
    def get_pending_invoices(self, customer_id: int) -> List[Dict]:
        """Busca faturas pendentes do cliente"""
        try:
            params = {
                'cliente_id': customer_id,
                'status': 'aberto',
                'order': 'vencimento ASC'
            }
            
            response = self.api.get('fn_areceber', params=params)
            
            if response and 'registros' in response:
                return response['registros']
            
            return []
            
        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar faturas pendentes do cliente {customer_id}: {e}")
            return []
    
    def get_invoice_details(self, invoice_id: int) -> Optional[Dict]:
        """Busca detalhes de uma fatura específica"""
        cache_key = f"ixc_invoice_{invoice_id}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            response = self.api.get(f'fn_areceber/{invoice_id}')
            
            if response:
                # Cache por 10 minutos
                cache.set(cache_key, response, 600)
                return response
                
        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar detalhes da fatura {invoice_id}: {e}")
            
        return None
    
    def get_invoice_pdf(self, invoice_id: int) -> Optional[bytes]:
        """Baixa PDF da fatura"""
        try:
            # Endpoint para download do PDF
            response = self.api.session.get(
                f"{self.api.base_url}/fn_areceber/{invoice_id}/pdf",
                headers=self.api.session.headers
            )
            response.raise_for_status()
            
            return response.content
            
        except Exception as e:
            logger.error(f"Erro ao baixar PDF da fatura {invoice_id}: {e}")
            return None


class SupportService:
    """Serviço para operações de suporte e ordens de serviço"""

    def __init__(self):
        self.api = IXCSoftAPIClient()

    def get_customer_tickets(self, customer_id: int, limit: int = 20) -> List[Dict]:
        """Busca ordens de serviço (tickets) do cliente"""
        cache_key = f"ixc_tickets_{customer_id}_{limit}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            params = {
                'cliente_id': customer_id,
                'limit': limit,
                'order': 'data_abertura DESC'
            }

            response = self.api.get('su_oss', params=params)

            tickets = []
            if response and 'registros' in response:
                tickets = response['registros']

                # Cache por 2 minutos (dados dinâmicos)
                cache.set(cache_key, tickets, 120)

            return tickets

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar tickets do cliente {customer_id}: {e}")
            return []

    def get_ticket_details(self, ticket_id: int) -> Optional[Dict]:
        """Busca detalhes de um ticket específico"""
        cache_key = f"ixc_ticket_{ticket_id}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            response = self.api.get(f'su_oss/{ticket_id}')

            if response:
                # Cache por 5 minutos
                cache.set(cache_key, response, 300)
                return response

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar detalhes do ticket {ticket_id}: {e}")

        return None

    def create_ticket(self, customer_id: int, ticket_data: Dict) -> Optional[Dict]:
        """Cria um novo ticket de suporte"""
        try:
            # Dados obrigatórios para criação
            payload = {
                'cliente_id': customer_id,
                'assunto_id': ticket_data.get('subject_id'),
                'descricao': ticket_data.get('description'),
                'prioridade': ticket_data.get('priority', 'normal'),
                'tipo': ticket_data.get('type', 'suporte'),
                'data_abertura': ticket_data.get('created_at'),
            }

            # Remove campos vazios
            payload = {k: v for k, v in payload.items() if v is not None}

            response = self.api.post('su_oss', data=payload)

            if response:
                # Invalida cache de tickets do cliente
                # Note: Em produção, usar Redis com pattern matching para cache_pattern
                logger.info(f"Ticket criado com sucesso: {response.get('id')}")
                return response

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao criar ticket para cliente {customer_id}: {e}")

        return None

    def get_ticket_subjects(self) -> List[Dict]:
        """Busca lista de assuntos disponíveis para tickets"""
        cache_key = "ixc_ticket_subjects"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            response = self.api.get('su_oss_assunto')

            subjects = []
            if response and 'registros' in response:
                subjects = response['registros']

                # Cache por 1 hora (dados relativamente estáticos)
                cache.set(cache_key, subjects, 3600)

            return subjects

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar assuntos de tickets: {e}")
            return []

    def add_ticket_response(self, ticket_id: int, response_data: Dict) -> bool:
        """Adiciona resposta a um ticket"""
        try:
            payload = {
                'oss_id': ticket_id,
                'resposta': response_data.get('message'),
                'tipo': response_data.get('type', 'cliente'),
                'data_resposta': response_data.get('created_at'),
            }

            response = self.api.post('su_oss_resposta', data=payload)

            if response:
                # Invalida cache do ticket
                cache.delete(f"ixc_ticket_{ticket_id}")
                logger.info(f"Resposta adicionada ao ticket {ticket_id}")
                return True

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao adicionar resposta ao ticket {ticket_id}: {e}")

        return False


class ConnectionService:
    """
    Serviço para operações de conexão e dados Radius.

    Integra com a API da IXCSoft para buscar dados de conexão,
    histórico de uso e estatísticas de velocidade.
    """

    def __init__(self):
        self.api = IXCSoftAPIClient()

    def get_customer_connections(
        self,
        customer_id: int,
        start_date: str = None,
        end_date: str = None,
        limit: int = 100
    ) -> List[Dict]:
        """
        Busca histórico de conexões do cliente.

        Args:
            customer_id: ID do cliente na IXCSoft
            start_date: Data inicial (YYYY-MM-DD)
            end_date: Data final (YYYY-MM-DD)
            limit: Limite de registros

        Returns:
            Lista de conexões do cliente
        """
        cache_key = f"ixc_connections_{customer_id}_{start_date}_{end_date}_{limit}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            params = {
                'cliente_id': customer_id,
                'limit': limit,
                'order': 'data_conexao DESC'
            }

            # Adiciona filtros de data se fornecidos
            if start_date:
                params['data_conexao_inicio'] = start_date
            if end_date:
                params['data_conexao_fim'] = end_date

            response = self.api.get('radius_conexoes', params=params)

            connections = []
            if response and 'registros' in response:
                connections = response['registros']

                # Cache por 10 minutos (dados históricos)
                cache.set(cache_key, connections, 600)

            return connections

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar conexões do cliente {customer_id}: {e}")
            return []

    def get_customer_usage_summary(
        self,
        customer_id: int,
        period: str = 'month'
    ) -> Optional[Dict]:
        """
        Busca resumo de uso do cliente.

        Args:
            customer_id: ID do cliente na IXCSoft
            period: Período ('day', 'week', 'month', 'year')

        Returns:
            Resumo de uso com estatísticas
        """
        cache_key = f"ixc_usage_summary_{customer_id}_{period}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            params = {
                'cliente_id': customer_id,
                'periodo': period,
                'incluir_graficos': True
            }

            response = self.api.get('radius_uso_resumo', params=params)

            if response:
                # Cache por 30 minutos
                cache.set(cache_key, response, 1800)
                return response

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar resumo de uso do cliente {customer_id}: {e}")

        return None

    def get_customer_speed_tests(
        self,
        customer_id: int,
        limit: int = 50
    ) -> List[Dict]:
        """
        Busca testes de velocidade do cliente.

        Args:
            customer_id: ID do cliente na IXCSoft
            limit: Limite de registros

        Returns:
            Lista de testes de velocidade
        """
        cache_key = f"ixc_speed_tests_{customer_id}_{limit}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            params = {
                'cliente_id': customer_id,
                'limit': limit,
                'order': 'data_teste DESC'
            }

            response = self.api.get('radius_testes_velocidade', params=params)

            speed_tests = []
            if response and 'registros' in response:
                speed_tests = response['registros']

                # Cache por 15 minutos
                cache.set(cache_key, speed_tests, 900)

            return speed_tests

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar testes de velocidade do cliente {customer_id}: {e}")
            return []

    def get_customer_bandwidth_usage(
        self,
        customer_id: int,
        date: str = None
    ) -> Optional[Dict]:
        """
        Busca uso de banda do cliente por data.

        Args:
            customer_id: ID do cliente na IXCSoft
            date: Data específica (YYYY-MM-DD), padrão hoje

        Returns:
            Dados de uso de banda
        """
        if not date:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')

        cache_key = f"ixc_bandwidth_{customer_id}_{date}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            params = {
                'cliente_id': customer_id,
                'data': date,
                'detalhado': True
            }

            response = self.api.get('radius_banda_uso', params=params)

            if response:
                # Cache por 5 minutos (dados em tempo real)
                cache.set(cache_key, response, 300)
                return response

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar uso de banda do cliente {customer_id}: {e}")

        return None

    def get_customer_plan_details(self, customer_id: int) -> Optional[Dict]:
        """
        Busca detalhes do plano contratado pelo cliente.

        Args:
            customer_id: ID do cliente na IXCSoft

        Returns:
            Detalhes do plano contratado
        """
        cache_key = f"ixc_plan_details_{customer_id}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            response = self.api.get(f'cliente/{customer_id}/plano')

            if response:
                # Cache por 1 hora (dados relativamente estáticos)
                cache.set(cache_key, response, 3600)
                return response

        except IXCSoftAPIError as e:
            logger.error(f"Erro ao buscar detalhes do plano do cliente {customer_id}: {e}")

        return None
