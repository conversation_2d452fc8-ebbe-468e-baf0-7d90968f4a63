#!/usr/bin/env python
"""
Script de setup inicial para Railway
Cria usuário admin e dados de teste
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'provedor.settings')
django.setup()

from django.contrib.auth import get_user_model
from support.models import TicketSubject

User = get_user_model()

def setup_initial_data():
    """Setup inicial do sistema"""
    
    print("🚀 Configurando sistema inicial...")
    
    # 1. Criar usuário admin com CPF válido
    cpf = '11144477735'  # CPF válido para teste
    if not User.objects.filter(cpf=cpf).exists():
        admin = User.objects.create_user(
            cpf=cpf,
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='<PERSON><PERSON><PERSON>',
            is_staff=True,
            is_superuser=True,
            is_active=True
        )
        print(f"✅ Admin criado - CPF: {cpf}")
    else:
        print(f"✅ Admin já existe - CPF: {cpf}")

    # 2. Criar usuário de teste com CPF válido
    test_cpf = '52998224725'  # CPF válido para teste
    if not User.objects.filter(cpf=test_cpf).exists():
        test_user = User.objects.create_user(
            cpf=test_cpf,
            email='<EMAIL>',
            password='teste123',
            first_name='Cliente',
            last_name='Teste',
            is_active=True
        )
        print(f"✅ Cliente teste criado - CPF: {test_cpf}")
    else:
        print(f"✅ Cliente teste já existe - CPF: {test_cpf}")
    
    # 3. Criar assuntos de tickets
    subjects = [
        {'ixc_subject_id': 1, 'name': 'Problema de Conexão', 'description': 'Problemas relacionados à conectividade'},
        {'ixc_subject_id': 2, 'name': 'Suporte Técnico', 'description': 'Suporte técnico geral'},
        {'ixc_subject_id': 3, 'name': 'Financeiro', 'description': 'Questões financeiras e cobrança'},
        {'ixc_subject_id': 4, 'name': 'Instalação', 'description': 'Solicitações de instalação'},
        {'ixc_subject_id': 5, 'name': 'Cancelamento', 'description': 'Solicitações de cancelamento'},
    ]
    
    for subject_data in subjects:
        subject, created = TicketSubject.objects.get_or_create(
            ixc_subject_id=subject_data['ixc_subject_id'],
            defaults={
                'name': subject_data['name'],
                'description': subject_data['description'],
                'is_active': True
            }
        )
        if created:
            print(f"✅ Assunto criado: {subject.name}")
    
    print(f"""
🎉 SETUP CONCLUÍDO!

🔑 CREDENCIAIS:

👨‍💼 ADMINISTRADOR:
   CPF: 11144477735
   Senha: admin123
   Acesso: /admin/ e /auth/login/

👤 CLIENTE TESTE:
   CPF: 52998224725
   Senha: teste123
   Acesso: /auth/login/

🌐 URLS IMPORTANTES:
   • Login: /auth/login/
   • Admin: /admin/
   • Dashboard: /dashboard/
   • Novo Chamado: /suporte/novo/

⚠️  Altere as senhas após o primeiro acesso!
""")

if __name__ == "__main__":
    setup_initial_data()
