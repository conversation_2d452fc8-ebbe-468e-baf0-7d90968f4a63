"""
Views para o portal do cliente.

Este módulo implementa as views principais do portal,
incluindo dashboard, perfil do usuário e funcionalidades básicas.
"""

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import UpdateView
from django.contrib import messages
from django.urls import reverse_lazy
from django.contrib.auth import get_user_model
from django.db import models
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@login_required
def dashboard(request):
    """Dashboard principal do cliente"""
    context = {
        'user': request.user,
        'title': 'Dashboard'
    }
    return render(request, 'customer_portal/dashboard.html', context)


class UserProfileView(LoginRequiredMixin, UpdateView):
    """
    View para visualizar e editar perfil do usuário.

    Permite ao usuário atualizar seus dados pessoais,
    incluindo informações de contato e preferências.
    """

    model = User
    template_name = 'customer_portal/profile.html'
    success_url = reverse_lazy('customer_portal:profile')

    fields = [
        'first_name', 'last_name', 'email', 'phone',
        'address', 'city', 'state', 'zip_code',
        'receive_email_notifications', 'receive_push_notifications'
    ]

    def get_object(self):
        """Retorna o usuário atual"""
        return self.request.user

    def get_context_data(self, **kwargs):
        """Adiciona dados extras ao contexto"""
        context = super().get_context_data(**kwargs)
        context.update({
            'title': 'Meu Perfil',
            'page_title': 'Perfil'
        })
        return context

    def form_valid(self, form):
        """Processa formulário válido"""
        messages.success(
            self.request,
            'Perfil atualizado com sucesso!'
        )
        logger.info(f"Perfil atualizado para usuário {self.request.user.cpf}")
        return super().form_valid(form)

    def form_invalid(self, form):
        """Processa formulário inválido"""
        messages.error(
            self.request,
            'Erro ao atualizar perfil. Verifique os dados informados.'
        )
        return super().form_invalid(form)


@login_required
def profile_view(request):
    """
    View alternativa para perfil (function-based).

    Pode ser usada para funcionalidades mais específicas
    que não se adequam ao UpdateView.
    """
    if request.method == 'POST':
        # Processar atualizações específicas
        pass

    context = {
        'user': request.user,
        'title': 'Meu Perfil',
        'page_title': 'Perfil'
    }
    return render(request, 'customer_portal/profile.html', context)


@login_required
def global_search(request):
    """
    View para busca global no sistema.

    Busca em faturas, tickets, notificações e outros dados
    do usuário baseado no termo de busca.
    """
    query = request.GET.get('q', '').strip()
    results = {
        'invoices': [],
        'tickets': [],
        'notifications': [],
        'speed_tests': []
    }

    if query and len(query) >= 2:
        try:
            # Buscar faturas
            from billing.models import Invoice
            invoices = Invoice.objects.filter(
                customer=request.user
            ).filter(
                models.Q(description__icontains=query) |
                models.Q(id__icontains=query)
            )[:5]
            results['invoices'] = invoices

            # Buscar tickets
            from support.models import Ticket
            tickets = Ticket.objects.filter(
                customer=request.user
            ).filter(
                models.Q(title__icontains=query) |
                models.Q(description__icontains=query) |
                models.Q(id__icontains=query)
            )[:5]
            results['tickets'] = tickets

            # Buscar notificações
            notifications = request.user.notifications.filter(
                models.Q(title__icontains=query) |
                models.Q(message__icontains=query)
            )[:5]
            results['notifications'] = notifications

            # Buscar testes de velocidade
            from connections.models import SpeedTest
            speed_tests = SpeedTest.objects.filter(
                customer=request.user,
                server_name__icontains=query
            )[:5]
            results['speed_tests'] = speed_tests

        except Exception as e:
            logger.error(f"Erro na busca global: {e}")

    # Se for requisição AJAX, retornar JSON
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        from django.http import JsonResponse
        return JsonResponse({
            'success': True,
            'query': query,
            'results': {
                'invoices': [
                    {
                        'id': inv.id,
                        'description': inv.description,
                        'amount': str(inv.amount),
                        'due_date': inv.due_date.strftime('%d/%m/%Y'),
                        'url': f'/faturas/{inv.id}/'
                    } for inv in results['invoices']
                ],
                'tickets': [
                    {
                        'id': ticket.id,
                        'title': ticket.title,
                        'status': ticket.get_status_display(),
                        'created_at': ticket.created_at.strftime('%d/%m/%Y'),
                        'url': f'/suporte/{ticket.id}/'
                    } for ticket in results['tickets']
                ],
                'notifications': [
                    {
                        'id': notif.id,
                        'title': notif.title,
                        'message': notif.message[:100],
                        'created_at': notif.created_at.strftime('%d/%m/%Y'),
                        'url': notif.url or '#'
                    } for notif in results['notifications']
                ],
                'speed_tests': [
                    {
                        'id': test.id,
                        'download_speed': test.download_speed,
                        'upload_speed': test.upload_speed,
                        'test_date': test.test_date.strftime('%d/%m/%Y %H:%M'),
                        'url': '/conexoes/velocidade/'
                    } for test in results['speed_tests']
                ]
            }
        })

    # Renderizar template normal
    context = {
        'query': query,
        'results': results,
        'title': 'Busca Global'
    }
    return render(request, 'customer_portal/search.html', context)
