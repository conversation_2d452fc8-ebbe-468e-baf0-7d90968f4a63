# Generated by Django 4.2.7 on 2025-06-27 12:24

import django.contrib.auth.models
import django.core.validators
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('cpf', models.CharField(help_text='CPF do cliente (com ou sem formatação)', max_length=14, unique=True, validators=[django.core.validators.RegexValidator(message='CPF deve estar no formato XXX.XXX.XXX-XX ou apenas números', regex='^\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}$|^\\d{11}$')])),
                ('phone', models.CharField(blank=True, max_length=15, validators=[django.core.validators.RegexValidator(message='Telefone deve estar no formato (XX) XXXXX-XXXX', regex='^\\(\\d{2}\\)\\s\\d{4,5}-\\d{4}$|^\\d{10,11}$')])),
                ('ixc_customer_id', models.IntegerField(blank=True, help_text='ID do cliente no sistema IXCSoft', null=True)),
                ('is_active_customer', models.BooleanField(default=True, help_text='Cliente ativo no sistema')),
                ('first_login', models.DateTimeField(blank=True, help_text='Data do primeiro login do cliente', null=True)),
                ('receive_email_notifications', models.BooleanField(default=True, help_text='Receber notificações por email')),
                ('receive_push_notifications', models.BooleanField(default=True, help_text='Receber notificações push')),
                ('fcm_token', models.TextField(blank=True, help_text='Token FCM para notificações push')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'Cliente',
                'verbose_name_plural': 'Clientes',
                'db_table': 'auth_customer',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
