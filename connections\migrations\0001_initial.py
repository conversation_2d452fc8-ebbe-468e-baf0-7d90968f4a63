# Generated by Django 4.2.7 on 2025-06-27 15:56

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SpeedTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ixc_test_id', models.CharField(help_text='ID do teste na IXCSoft', max_length=100, unique=True)),
                ('test_date', models.DateTimeField(help_text='Data e hora do teste')),
                ('server_name', models.CharField(blank=True, help_text='Nome do servidor de teste', max_length=100)),
                ('server_location', models.CharField(blank=True, help_text='Localização do servidor', max_length=100)),
                ('download_speed', models.FloatField(help_text='Velocidade de download em Mbps', validators=[django.core.validators.MinValueValidator(0)])),
                ('upload_speed', models.FloatField(help_text='Velocidade de upload em Mbps', validators=[django.core.validators.MinValueValidator(0)])),
                ('ping', models.FloatField(help_text='Latência (ping) em ms', validators=[django.core.validators.MinValueValidator(0)])),
                ('jitter', models.FloatField(blank=True, help_text='Jitter em ms', null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('contracted_download', models.FloatField(blank=True, help_text='Velocidade contratada de download em Mbps', null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('contracted_upload', models.FloatField(blank=True, help_text='Velocidade contratada de upload em Mbps', null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP usado no teste', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='User agent do navegador')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(help_text='Cliente que realizou o teste', on_delete=django.db.models.deletion.CASCADE, related_name='speed_tests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Teste de Velocidade',
                'verbose_name_plural': 'Testes de Velocidade',
                'ordering': ['-test_date'],
                'indexes': [models.Index(fields=['customer', 'test_date'], name='connections_custome_ec7a6e_idx'), models.Index(fields=['test_date'], name='connections_test_da_a427e6_idx')],
            },
        ),
        migrations.CreateModel(
            name='ConnectionSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ixc_session_id', models.CharField(help_text='ID da sessão na IXCSoft', max_length=100, unique=True)),
                ('start_time', models.DateTimeField(help_text='Início da sessão')),
                ('end_time', models.DateTimeField(blank=True, help_text='Fim da sessão', null=True)),
                ('duration_seconds', models.PositiveIntegerField(default=0, help_text='Duração em segundos')),
                ('ip_address', models.GenericIPAddressField(help_text='Endereço IP atribuído')),
                ('mac_address', models.CharField(blank=True, help_text='Endereço MAC do dispositivo', max_length=17)),
                ('nas_ip', models.GenericIPAddressField(blank=True, help_text='IP do NAS (Network Access Server)', null=True)),
                ('bytes_uploaded', models.BigIntegerField(default=0, help_text='Bytes enviados (upload)')),
                ('bytes_downloaded', models.BigIntegerField(default=0, help_text='Bytes recebidos (download)')),
                ('packets_uploaded', models.BigIntegerField(default=0, help_text='Pacotes enviados')),
                ('packets_downloaded', models.BigIntegerField(default=0, help_text='Pacotes recebidos')),
                ('status', models.CharField(choices=[('active', 'Ativa'), ('terminated', 'Finalizada'), ('timeout', 'Timeout'), ('error', 'Erro')], default='active', help_text='Status da sessão', max_length=20)),
                ('termination_cause', models.CharField(blank=True, help_text='Causa da terminação', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(help_text='Cliente da sessão', on_delete=django.db.models.deletion.CASCADE, related_name='connection_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sessão de Conexão',
                'verbose_name_plural': 'Sessões de Conexão',
                'ordering': ['-start_time'],
                'indexes': [models.Index(fields=['customer', 'start_time'], name='connections_custome_01c13d_idx'), models.Index(fields=['status'], name='connections_status_0ac84e_idx'), models.Index(fields=['start_time'], name='connections_start_t_0acfe6_idx')],
            },
        ),
        migrations.CreateModel(
            name='BandwidthUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_type', models.CharField(choices=[('hour', 'Hora'), ('day', 'Dia'), ('week', 'Semana'), ('month', 'Mês')], help_text='Tipo de período', max_length=10)),
                ('period_start', models.DateTimeField(help_text='Início do período')),
                ('period_end', models.DateTimeField(help_text='Fim do período')),
                ('bytes_uploaded', models.BigIntegerField(default=0, help_text='Total de bytes enviados')),
                ('bytes_downloaded', models.BigIntegerField(default=0, help_text='Total de bytes recebidos')),
                ('peak_upload_speed', models.BigIntegerField(default=0, help_text='Pico de velocidade de upload em bps')),
                ('peak_download_speed', models.BigIntegerField(default=0, help_text='Pico de velocidade de download em bps')),
                ('avg_upload_speed', models.BigIntegerField(default=0, help_text='Velocidade média de upload em bps')),
                ('avg_download_speed', models.BigIntegerField(default=0, help_text='Velocidade média de download em bps')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(help_text='Cliente do uso', on_delete=django.db.models.deletion.CASCADE, related_name='bandwidth_usage', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Uso de Banda',
                'verbose_name_plural': 'Uso de Banda',
                'ordering': ['-period_start'],
                'indexes': [models.Index(fields=['customer', 'period_type', 'period_start'], name='connections_custome_d96916_idx'), models.Index(fields=['period_start'], name='connections_period__31fecf_idx')],
                'unique_together': {('customer', 'period_type', 'period_start')},
            },
        ),
    ]
