{% extends 'base.html' %}

{% block title %}Minhas Faturas - Portal do Cliente{% endblock %}
{% block page_title %}Faturas{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-invoice me-2"></i>Minhas Faturas
            </h1>
            <p class="text-muted">Visualize e gerencie suas faturas</p>
        </div>
    </div>
    
    <!-- Estatísticas -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total de Faturas</h6>
                            <h4 class="mb-0">{{ stats.total_invoices }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Em Aberto</h6>
                            <h4 class="mb-0">{{ stats.pending_invoices }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Vencidas</h6>
                            <h4 class="mb-0">{{ stats.overdue_invoices }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Valor Pendente</h6>
                            <h4 class="mb-0">R$ {{ stats.total_pending_amount|floatformat:2 }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">Todos</option>
                                <option value="aberto" {% if status_filter == 'aberto' %}selected{% endif %}>Em Aberto</option>
                                <option value="pago" {% if status_filter == 'pago' %}selected{% endif %}>Pago</option>
                                <option value="vencido" {% if status_filter == 'vencido' %}selected{% endif %}>Vencido</option>
                                <option value="cancelado" {% if status_filter == 'cancelado' %}selected{% endif %}>Cancelado</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i>Filtrar
                            </button>
                            <a href="{% url 'billing:invoice_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Limpar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Lista de Faturas -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Lista de Faturas
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Fatura</th>
                                    <th>Descrição</th>
                                    <th>Vencimento</th>
                                    <th>Valor</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <strong>#{{ invoice.ixc_invoice_id }}</strong>
                                    </td>
                                    <td>{{ invoice.description|truncatechars:50 }}</td>
                                    <td>
                                        {{ invoice.due_date|date:"d/m/Y" }}
                                        {% if invoice.is_overdue %}
                                            <br><small class="text-danger">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                Vencida
                                            </small>
                                        {% elif invoice.days_until_due is not None and invoice.days_until_due <= 7 %}
                                            <br><small class="text-warning">
                                                <i class="fas fa-clock"></i>
                                                {{ invoice.days_until_due }} dias
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ invoice.formatted_amount }}</strong>
                                    </td>
                                    <td>
                                        {% if invoice.status == 'aberto' %}
                                            <span class="badge bg-warning">Em Aberto</span>
                                        {% elif invoice.status == 'pago' %}
                                            <span class="badge bg-success">Pago</span>
                                        {% elif invoice.status == 'vencido' %}
                                            <span class="badge bg-danger">Vencido</span>
                                        {% elif invoice.status == 'cancelado' %}
                                            <span class="badge bg-secondary">Cancelado</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'billing:invoice_detail' invoice.id %}" 
                                               class="btn btn-outline-primary" title="Ver Detalhes">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if invoice.status == 'aberto' %}
                                            <a href="{% url 'billing:download_pdf' invoice.id %}" 
                                               class="btn btn-outline-success" title="Baixar PDF">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Paginação -->
                    {% if page_obj.has_other_pages %}
                    <div class="card-footer">
                        <nav aria-label="Navegação de páginas">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhuma fatura encontrada</h5>
                        <p class="text-muted">
                            {% if status_filter %}
                                Não há faturas com o status selecionado.
                            {% else %}
                                Você não possui faturas no momento.
                            {% endif %}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
