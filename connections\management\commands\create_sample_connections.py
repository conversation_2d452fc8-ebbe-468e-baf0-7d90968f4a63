"""
Comando para criar dados de exemplo de conexões.

Este comando cria sessões de conexão, testes de velocidade e
dados de uso de banda para demonstração do sistema.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random
import uuid

from connections.models import ConnectionSession, SpeedTest, BandwidthUsage

User = get_user_model()


class Command(BaseCommand):
    """Comando para criar dados de exemplo do sistema de conexões"""
    
    help = 'Cria dados de exemplo de conexões, testes de velocidade e uso de banda'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cpf',
            type=str,
            help='CPF do cliente para criar dados',
            default='111.444.777-35'
        )
        parser.add_argument(
            '--days',
            type=int,
            help='Número de dias de histórico para criar',
            default=30
        )

    def handle(self, *args, **options):
        cpf = options['cpf']
        days = options['days']
        
        try:
            user = User.objects.get(cpf=cpf)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Usuário com CPF {cpf} não encontrado')
            )
            return
        
        # Define ID do cliente na IXCSoft se não existir
        if not user.ixc_customer_id:
            user.ixc_customer_id = 12345
            user.save()
        
        # Remove dados existentes
        ConnectionSession.objects.filter(customer=user).delete()
        SpeedTest.objects.filter(customer=user).delete()
        BandwidthUsage.objects.filter(customer=user).delete()
        
        # Cria dados de exemplo
        self.create_connection_sessions(user, days)
        self.create_speed_tests(user, days)
        self.create_bandwidth_usage(user, days)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Criados dados de conexão para {user.full_name} '
                f'({days} dias de histórico)'
            )
        )

    def create_connection_sessions(self, user, days):
        """Cria sessões de conexão de exemplo"""
        now = timezone.now()
        
        for day in range(days):
            date = now - timedelta(days=day)
            
            # 2-5 sessões por dia
            sessions_per_day = random.randint(2, 5)
            
            for session_num in range(sessions_per_day):
                # Horário aleatório no dia
                start_hour = random.randint(6, 22)
                start_minute = random.randint(0, 59)
                
                start_time = date.replace(
                    hour=start_hour, 
                    minute=start_minute, 
                    second=0, 
                    microsecond=0
                )
                
                # Duração da sessão (30min a 8h)
                duration_minutes = random.randint(30, 480)
                end_time = start_time + timedelta(minutes=duration_minutes)
                
                # Dados de tráfego baseados na duração
                base_speed_mbps = random.uniform(50, 120)  # Velocidade base
                bytes_per_second = (base_speed_mbps * 1024 * 1024) / 8  # Converter para bytes
                
                total_bytes = int(bytes_per_second * duration_minutes * 60)
                upload_ratio = random.uniform(0.1, 0.3)  # Upload é 10-30% do total
                
                bytes_uploaded = int(total_bytes * upload_ratio)
                bytes_downloaded = total_bytes - bytes_uploaded
                
                # IP aleatório da rede local
                ip_address = f"192.168.1.{random.randint(100, 200)}"
                
                session = ConnectionSession.objects.create(
                    ixc_session_id=f"sess_{uuid.uuid4().hex[:12]}",
                    customer=user,
                    start_time=start_time,
                    end_time=end_time,
                    duration_seconds=duration_minutes * 60,
                    ip_address=ip_address,
                    mac_address=self.generate_mac_address(),
                    bytes_uploaded=bytes_uploaded,
                    bytes_downloaded=bytes_downloaded,
                    packets_uploaded=random.randint(1000, 10000),
                    packets_downloaded=random.randint(5000, 50000),
                    status='terminated',
                    termination_cause='User-Request'
                )
                
                if day == 0 and session_num == 0:  # Primeira sessão do dia atual
                    self.stdout.write(f'Sessão criada: {session.duration_formatted}, {session.total_mb:.1f}MB')

    def create_speed_tests(self, user, days):
        """Cria testes de velocidade de exemplo"""
        now = timezone.now()
        
        # 1-3 testes por semana
        test_days = random.sample(range(days), min(days // 2, 15))
        
        servers = [
            ('Speedtest.net - São Paulo', 'São Paulo, SP'),
            ('Ookla - Rio de Janeiro', 'Rio de Janeiro, RJ'),
            ('Fast.com - Netflix', 'São Paulo, SP'),
            ('Google Speed Test', 'São Paulo, SP'),
        ]
        
        # Velocidades contratadas
        contracted_download = 100.0
        contracted_upload = 50.0
        
        for day in test_days:
            test_date = now - timedelta(days=day)
            test_date = test_date.replace(
                hour=random.randint(10, 22),
                minute=random.randint(0, 59),
                second=0,
                microsecond=0
            )
            
            server_name, server_location = random.choice(servers)
            
            # Velocidades com variação realística
            download_variation = random.uniform(0.7, 1.1)  # 70% a 110% do contratado
            upload_variation = random.uniform(0.8, 1.2)    # 80% a 120% do contratado
            
            download_speed = contracted_download * download_variation
            upload_speed = contracted_upload * upload_variation
            
            # Ping baseado na qualidade da conexão
            base_ping = random.uniform(15, 45)
            if download_variation < 0.8:  # Conexão ruim = ping alto
                ping = base_ping * random.uniform(1.5, 3.0)
            else:
                ping = base_ping
            
            SpeedTest.objects.create(
                ixc_test_id=f"test_{uuid.uuid4().hex[:12]}",
                customer=user,
                test_date=test_date,
                server_name=server_name,
                server_location=server_location,
                download_speed=download_speed,
                upload_speed=upload_speed,
                ping=ping,
                jitter=random.uniform(1, 10),
                contracted_download=contracted_download,
                contracted_upload=contracted_upload,
                ip_address=f"192.168.1.{random.randint(100, 200)}"
            )
        
        self.stdout.write(f'Criados {len(test_days)} testes de velocidade')

    def create_bandwidth_usage(self, user, days):
        """Cria dados de uso de banda por dia"""
        now = timezone.now()
        
        for day in range(min(days, 30)):  # Máximo 30 dias
            date = now - timedelta(days=day)
            period_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            period_end = period_start + timedelta(days=1)
            
            # Uso diário baseado em padrões realísticos
            base_usage_gb = random.uniform(5, 25)  # 5-25GB por dia
            
            # Distribuição 80/20 download/upload
            total_bytes = int(base_usage_gb * 1024 * 1024 * 1024)
            bytes_downloaded = int(total_bytes * 0.8)
            bytes_uploaded = total_bytes - bytes_downloaded
            
            # Velocidades de pico (em bps)
            peak_download_bps = random.randint(80, 120) * 1024 * 1024  # 80-120 Mbps
            peak_upload_bps = random.randint(40, 60) * 1024 * 1024    # 40-60 Mbps
            
            # Velocidades médias (60-80% do pico)
            avg_download_bps = int(peak_download_bps * random.uniform(0.6, 0.8))
            avg_upload_bps = int(peak_upload_bps * random.uniform(0.6, 0.8))
            
            BandwidthUsage.objects.create(
                customer=user,
                period_type='day',
                period_start=period_start,
                period_end=period_end,
                bytes_uploaded=bytes_uploaded,
                bytes_downloaded=bytes_downloaded,
                peak_upload_speed=peak_upload_bps,
                peak_download_speed=peak_download_bps,
                avg_upload_speed=avg_upload_bps,
                avg_download_speed=avg_download_bps
            )
        
        self.stdout.write(f'Criados dados de uso para {min(days, 30)} dias')

    def generate_mac_address(self):
        """Gera um endereço MAC aleatório"""
        mac = [0x00, 0x16, 0x3e,
               random.randint(0x00, 0x7f),
               random.randint(0x00, 0xff),
               random.randint(0x00, 0xff)]
        return ':'.join(map(lambda x: "%02x" % x, mac))
