# Emergency fix for ALLOWED_HOSTS
# Add this to the end of your settings.py file temporarily

import os

# Railway emergency fix
if 'DATABASE_URL' in os.environ:
    ALLOWED_HOSTS = ['*']  # Allow all hosts in production
    print(f"Railway detected - ALLOWED_HOSTS set to: {ALLOWED_HOSTS}")
else:
    # Keep secure settings for local development
    if 'ALLOWED_HOSTS' not in locals():
        ALLOWED_HOSTS = ['localhost', '127.0.0.1']
