{% extends 'base.html' %}

{% block title %}Abrir Novo Chamado - Portal do Cliente{% endblock %}
{% block page_title %}Novo Chamado{% endblock %}

{% block extra_css %}
<style>
.create-ticket-header {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
}

.form-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.form-section h5 {
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray-200);
    display: flex;
    align-items: center;
    font-weight: 600;
}

.form-section h5 i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.help-section {
    background: linear-gradient(135deg, var(--info-color) 0%, #0ea5e9 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
}

.help-section h5 {
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.custom-subject-section {
    background: var(--gray-50);
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
    display: none;
}

.custom-subject-section.show {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.file-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: var(--gray-50);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.uploaded-files {
    margin-top: 1rem;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 0.75rem;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.file-item .file-info {
    flex: 1;
    display: flex;
    align-items: center;
}

.file-item .file-info i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.file-item .file-size {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-left: 0.5rem;
}

.file-item .remove-file {
    color: var(--danger-color);
    cursor: pointer;
    padding: 0.25rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-row .form-group {
    flex: 1;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-section {
        padding: 1.5rem;
    }

    .create-ticket-header {
        padding: 1.5rem;
    }

    .help-section {
        padding: 1.5rem;
    }
}

.progress-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.progress-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.progress-step::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    right: -50%;
    height: 2px;
    background: var(--gray-300);
    z-index: 1;
}

.progress-step:last-child::before {
    display: none;
}

.progress-step.active::before {
    background: var(--primary-color);
}

.progress-step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--gray-300);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: bold;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.progress-step.active .progress-step-circle {
    background: var(--primary-color);
}

.progress-step.completed .progress-step-circle {
    background: var(--success-color);
}

.progress-step-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.progress-step.active .progress-step-label {
    color: var(--primary-color);
    font-weight: 600;
}

.char-counter {
    font-size: 0.875rem;
    color: var(--gray-500);
    text-align: right;
    margin-top: 0.5rem;
}

.char-counter.warning {
    color: var(--warning-color);
}

.char-counter.danger {
    color: var(--danger-color);
}

.form-check-custom {
    background: var(--gray-50);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
}

.form-check-custom:hover {
    background: var(--gray-100);
}

.form-check-custom .form-check-input:checked ~ .form-check-label {
    color: var(--primary-color);
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Header -->
    <div class="create-ticket-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-1">
                    <i class="fas fa-plus-circle me-2"></i>Abrir Novo Chamado
                </h2>
                <p class="mb-0 opacity-75">
                    Preencha as informações abaixo para que possamos ajudá-lo da melhor forma
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex align-items-center justify-content-end">
                    <i class="fas fa-headset fa-2x me-2"></i>
                    <div>
                        <div class="fw-bold">Suporte 24/7</div>
                        <small class="opacity-75">Resposta em até 2 horas</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-indicator">
        <div class="progress-step active">
            <div class="progress-step-circle">1</div>
            <div class="progress-step-label">Assunto</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-circle">2</div>
            <div class="progress-step-label">Detalhes</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-circle">3</div>
            <div class="progress-step-label">Informações</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-circle">4</div>
            <div class="progress-step-label">Anexos</div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="help-section">
        <h5><i class="fas fa-lightbulb me-2"></i>Dicas para um Atendimento Mais Rápido</h5>
        <div class="row">
            <div class="col-md-3">
                <div class="d-flex align-items-start">
                    <i class="fas fa-check-circle me-2 mt-1"></i>
                    <div>
                        <strong>Seja específico</strong><br>
                        <small>Descreva o problema com detalhes</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-start">
                    <i class="fas fa-clock me-2 mt-1"></i>
                    <div>
                        <strong>Informe quando ocorreu</strong><br>
                        <small>Data e horário do problema</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-start">
                    <i class="fas fa-paperclip me-2 mt-1"></i>
                    <div>
                        <strong>Anexe evidências</strong><br>
                        <small>Screenshots ou documentos</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-start">
                    <i class="fas fa-tools me-2 mt-1"></i>
                    <div>
                        <strong>Tentativas de solução</strong><br>
                        <small>O que você já tentou fazer</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
        {% csrf_token %}

        <!-- Seção 1: Assunto -->
        <div class="form-section" data-step="1">
            <h5><i class="fas fa-tags"></i>Assunto do Chamado</h5>

            <div class="mb-3">
                <label for="{{ form.subject.id_for_label }}" class="form-label">{{ form.subject.label }}</label>
                {{ form.subject }}
                {% if form.subject.help_text %}
                    <div class="form-text">{{ form.subject.help_text }}</div>
                {% endif %}
                {% if form.subject.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.subject.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Opção de assunto personalizado -->
            <div class="form-check-custom">
                {{ form.use_custom_subject }}
                <label class="form-check-label" for="{{ form.use_custom_subject.id_for_label }}">
                    <i class="fas fa-plus-circle me-2"></i>{{ form.use_custom_subject.label }}
                </label>
            </div>

            <!-- Campos de assunto personalizado -->
            <div class="custom-subject-section" id="customSubjectSection">
                <div class="mb-3">
                    <label for="{{ form.custom_subject.id_for_label }}" class="form-label">{{ form.custom_subject.label }}</label>
                    {{ form.custom_subject }}
                    {% if form.custom_subject.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.custom_subject.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.custom_subject_reason.id_for_label }}" class="form-label">{{ form.custom_subject_reason.label }}</label>
                    {{ form.custom_subject_reason }}
                    {% if form.custom_subject_reason.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.custom_subject_reason.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Seção 2: Classificação -->
        <div class="form-section" data-step="2">
            <h5><i class="fas fa-flag"></i>Classificação do Chamado</h5>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.priority.id_for_label }}" class="form-label">{{ form.priority.label }}</label>
                    {{ form.priority }}
                    {% if form.priority.help_text %}
                        <div class="form-text">{{ form.priority.help_text }}</div>
                    {% endif %}
                    {% if form.priority.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.priority.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.ticket_type.id_for_label }}" class="form-label">{{ form.ticket_type.label }}</label>
                    {{ form.ticket_type }}
                    {% if form.ticket_type.help_text %}
                        <div class="form-text">{{ form.ticket_type.help_text }}</div>
                    {% endif %}
                    {% if form.ticket_type.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.ticket_type.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Seção 3: Detalhes Básicos -->
        <div class="form-section" data-step="2">
            <h5><i class="fas fa-edit"></i>Detalhes do Problema</h5>

            <div class="mb-3">
                <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }}</label>
                {{ form.title }}
                {% if form.title.help_text %}
                    <div class="form-text">{{ form.title.help_text }}</div>
                {% endif %}
                {% if form.title.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.title.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                {{ form.description }}
                <div class="char-counter" id="descriptionCounter">0 / 1000 caracteres</div>
                {% if form.description.help_text %}
                    <div class="form-text">{{ form.description.help_text }}</div>
                {% endif %}
                {% if form.description.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.description.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.problem_occurred_at.id_for_label }}" class="form-label">{{ form.problem_occurred_at.label }}</label>
                    {{ form.problem_occurred_at }}
                    {% if form.problem_occurred_at.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.problem_occurred_at.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.affected_equipment.id_for_label }}" class="form-label">{{ form.affected_equipment.label }}</label>
                    {{ form.affected_equipment }}
                    {% if form.affected_equipment.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.affected_equipment.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Seção 4: Informações Detalhadas -->
        <div class="form-section" data-step="3">
            <h5><i class="fas fa-info-circle"></i>Informações Adicionais</h5>

            <div class="mb-3">
                <label for="{{ form.error_message.id_for_label }}" class="form-label">{{ form.error_message.label }}</label>
                {{ form.error_message }}
                {% if form.error_message.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.error_message.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.steps_to_reproduce.id_for_label }}" class="form-label">{{ form.steps_to_reproduce.label }}</label>
                {{ form.steps_to_reproduce }}
                {% if form.steps_to_reproduce.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.steps_to_reproduce.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.attempted_solutions.id_for_label }}" class="form-label">{{ form.attempted_solutions.label }}</label>
                {{ form.attempted_solutions }}
                {% if form.attempted_solutions.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.attempted_solutions.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.impact_description.id_for_label }}" class="form-label">{{ form.impact_description.label }}</label>
                {{ form.impact_description }}
                {% if form.impact_description.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.impact_description.errors.0 }}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Seção 5: Informações Técnicas -->
        <div class="form-section" data-step="3">
            <h5><i class="fas fa-desktop"></i>Informações Técnicas</h5>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.operating_system.id_for_label }}" class="form-label">{{ form.operating_system.label }}</label>
                    {{ form.operating_system }}
                    {% if form.operating_system.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.operating_system.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.browser_version.id_for_label }}" class="form-label">{{ form.browser_version.label }}</label>
                    {{ form.browser_version }}
                    {% if form.browser_version.errors %}
                        <div class="text-danger small mt-1">
                            {{ form.browser_version.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.connection_type.id_for_label }}" class="form-label">{{ form.connection_type.label }}</label>
                {{ form.connection_type }}
                {% if form.connection_type.errors %}
                    <div class="text-danger small mt-1">
                        {{ form.connection_type.errors.0 }}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Seção 6: Anexos -->
        <div class="form-section" data-step="4">
            <h5><i class="fas fa-paperclip"></i>Anexos e Evidências</h5>

            <div class="mb-3">
                <label class="form-label">Anexos e Evidências</label>
                <div class="file-upload-area" id="fileUploadArea">
                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                    <p class="mb-2"><strong>Clique aqui ou arraste arquivos</strong></p>
                    <p class="mb-0 text-muted">Imagens, documentos, prints. Máximo 5MB por arquivo.</p>
                    <input type="file" id="fileInput" multiple
                           accept="image/*,.pdf,.doc,.docx,.txt,.zip,.rar"
                           style="display: none;">
                </div>
                <div id="uploadedFiles" class="uploaded-files"></div>
                <div class="form-text">Tipos permitidos: JPG, PNG, PDF, DOC, TXT, ZIP. Máximo 5MB por arquivo.</div>
                {{ form.attachments }}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex justify-content-between align-items-center">
            <a href="{% url 'support:ticket_list' %}" class="btn btn-outline-secondary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Voltar à Lista
            </a>
            <button type="submit" class="btn btn-warning btn-lg">
                <i class="fas fa-paper-plane me-2"></i>Abrir Chamado
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and file handling
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Add files to form data
            if (selectedFiles && selectedFiles.length > 0) {
                selectedFiles.forEach((file, index) => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.name = `attachment_${index}`;
                    fileInput.style.display = 'none';

                    // Create a new FileList with the file
                    const dt = new DataTransfer();
                    dt.items.add(file);
                    fileInput.files = dt.files;

                    form.appendChild(fileInput);
                });
            }

            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }

    // Custom subject toggle
    const customSubjectCheckbox = document.getElementById('{{ form.use_custom_subject.id_for_label }}');
    const customSubjectSection = document.getElementById('customSubjectSection');
    const subjectSelect = document.getElementById('{{ form.subject.id_for_label }}');

    if (customSubjectCheckbox && customSubjectSection) {
        customSubjectCheckbox.addEventListener('change', function() {
            if (this.checked) {
                customSubjectSection.classList.add('show');
                subjectSelect.disabled = true;
                subjectSelect.value = '';
            } else {
                customSubjectSection.classList.remove('show');
                subjectSelect.disabled = false;
            }
        });
    }

    // Character counter for description
    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
    const descriptionCounter = document.getElementById('descriptionCounter');

    if (descriptionField && descriptionCounter) {
        function updateCounter() {
            const length = descriptionField.value.length;
            const maxLength = 1000;
            descriptionCounter.textContent = `${length} / ${maxLength} caracteres`;

            if (length > maxLength * 0.9) {
                descriptionCounter.classList.add('danger');
                descriptionCounter.classList.remove('warning');
            } else if (length > maxLength * 0.7) {
                descriptionCounter.classList.add('warning');
                descriptionCounter.classList.remove('danger');
            } else {
                descriptionCounter.classList.remove('warning', 'danger');
            }
        }

        descriptionField.addEventListener('input', updateCounter);
        updateCounter(); // Initial count
    }

    // File upload handling
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadedFiles = document.getElementById('uploadedFiles');
    let selectedFiles = [];

    if (fileUploadArea && fileInput) {
        // Click to upload
        fileUploadArea.addEventListener('click', function() {
            fileInput.click();
        });

        // Drag and drop
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        // File input change
        fileInput.addEventListener('change', function() {
            const files = Array.from(this.files);
            handleFiles(files);
        });

        function handleFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    selectedFiles.push(file);
                    displayFile(file);
                }
            });
            updateFileInput();
        }

        function validateFile(file) {
            const maxSize = 5 * 1024 * 1024; // 5MB
            const allowedTypes = [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
                'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain', 'application/rtf', 'application/zip', 'application/x-rar-compressed'
            ];

            if (file.size > maxSize) {
                alert(`Arquivo "${file.name}" é muito grande. Máximo permitido: 5MB.`);
                return false;
            }

            if (!allowedTypes.includes(file.type)) {
                alert(`Tipo de arquivo "${file.type}" não permitido.`);
                return false;
            }

            return true;
        }

        function displayFile(file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file"></i>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">(${formatFileSize(file.size)})</span>
                </div>
                <i class="fas fa-times remove-file" onclick="removeFile('${file.name}')"></i>
            `;
            uploadedFiles.appendChild(fileItem);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function updateFileInput() {
            // Não precisamos atualizar o input original, vamos criar inputs hidden
            // para cada arquivo que será processado no submit
        }

        // Global function to remove files
        window.removeFile = function(fileName) {
            selectedFiles = selectedFiles.filter(file => file.name !== fileName);
            updateFileInput();

            // Remove from display
            const fileItems = uploadedFiles.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                if (item.querySelector('.file-name').textContent === fileName) {
                    item.remove();
                }
            });
        };
    }

    // Progress indicator
    const formSections = document.querySelectorAll('.form-section[data-step]');
    const progressSteps = document.querySelectorAll('.progress-step');

    function updateProgress() {
        let currentStep = 1;

        formSections.forEach(section => {
            const step = parseInt(section.dataset.step);
            const inputs = section.querySelectorAll('input, select, textarea');
            let hasValue = false;

            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    if (input.checked) hasValue = true;
                } else if (input.value.trim() !== '') {
                    hasValue = true;
                }
            });

            if (hasValue && step > currentStep) {
                currentStep = step;
            }
        });

        progressSteps.forEach((step, index) => {
            if (index < currentStep) {
                step.classList.add('completed');
                step.classList.remove('active');
            } else if (index === currentStep) {
                step.classList.add('active');
                step.classList.remove('completed');
            } else {
                step.classList.remove('active', 'completed');
            }
        });
    }

    // Update progress on input changes
    form.addEventListener('input', updateProgress);
    form.addEventListener('change', updateProgress);
    updateProgress(); // Initial update
});
</script>
{% endblock %}
