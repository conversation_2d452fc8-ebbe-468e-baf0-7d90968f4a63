"""
Formulários para o portal do cliente.

Este módulo implementa os formulários para edição de perfil
e outras funcionalidades do portal do cliente.
"""

from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
import re

User = get_user_model()


class UserProfileForm(forms.ModelForm):
    """
    Formulário para edição do perfil do usuário.
    
    Permite editar dados pessoais, contato e preferências
    com validações apropriadas.
    """
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone',
            'address', 'city', 'state', 'zip_code',
            'receive_email_notifications', 'receive_push_notifications'
        ]
        
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Seu primeiro nome'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Seu sobrenome'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '(11) 99999-9999',
                'data-mask': '(00) 00000-0000'
            }),
            'address': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Rua, número, complemento'
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Sua cidade'
            }),
            'state': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'SP',
                'maxlength': '2'
            }),
            'zip_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '00000-000',
                'data-mask': '00000-000'
            }),
            'receive_email_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'receive_push_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'first_name': 'Nome',
            'last_name': 'Sobrenome',
            'email': 'E-mail',
            'phone': 'Telefone',
            'address': 'Endereço',
            'city': 'Cidade',
            'state': 'Estado',
            'zip_code': 'CEP',
            'receive_email_notifications': 'Receber notificações por e-mail',
            'receive_push_notifications': 'Receber notificações push',
        }
        
        help_texts = {
            'email': 'Usado para login e notificações importantes',
            'phone': 'Formato: (11) 99999-9999',
            'state': 'Sigla do estado (ex: SP, RJ)',
            'zip_code': 'Formato: 00000-000',
        }
    
    def clean_phone(self):
        """Valida e formata o telefone"""
        phone = self.cleaned_data.get('phone')
        if phone:
            # Remove formatação
            phone_clean = re.sub(r'[^0-9]', '', phone)
            
            # Valida se tem 10 ou 11 dígitos
            if len(phone_clean) not in [10, 11]:
                raise ValidationError('Telefone deve ter 10 ou 11 dígitos')
            
            # Formata o telefone
            if len(phone_clean) == 11:
                return f"({phone_clean[:2]}) {phone_clean[2:7]}-{phone_clean[7:]}"
            else:
                return f"({phone_clean[:2]}) {phone_clean[2:6]}-{phone_clean[6:]}"
        
        return phone
    
    def clean_zip_code(self):
        """Valida e formata o CEP"""
        zip_code = self.cleaned_data.get('zip_code')
        if zip_code:
            # Remove formatação
            zip_clean = re.sub(r'[^0-9]', '', zip_code)
            
            # Valida se tem 8 dígitos
            if len(zip_clean) != 8:
                raise ValidationError('CEP deve ter 8 dígitos')
            
            # Formata o CEP
            return f"{zip_clean[:5]}-{zip_clean[5:]}"
        
        return zip_code
    
    def clean_state(self):
        """Valida o estado"""
        state = self.cleaned_data.get('state')
        if state:
            state = state.upper()
            
            # Lista de estados válidos
            valid_states = [
                'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO',
                'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI',
                'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
            ]
            
            if state not in valid_states:
                raise ValidationError('Estado inválido')
        
        return state
    
    def clean_email(self):
        """Valida se o email não está em uso por outro usuário"""
        email = self.cleaned_data.get('email')
        if email:
            # Verifica se outro usuário já usa este email
            existing_user = User.objects.filter(email=email).exclude(
                pk=self.instance.pk if self.instance else None
            ).first()
            
            if existing_user:
                raise ValidationError('Este e-mail já está em uso por outro usuário')
        
        return email
    
    def save(self, commit=True):
        """Salva o formulário com log"""
        user = super().save(commit=False)
        
        if commit:
            user.save()
            # Log da atualização
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Perfil atualizado para usuário {user.cpf}")
        
        return user


class PasswordChangeForm(forms.Form):
    """
    Formulário para alteração de senha.
    
    Permite ao usuário alterar sua senha atual
    com validações de segurança.
    """
    
    current_password = forms.CharField(
        label='Senha Atual',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite sua senha atual'
        }),
        help_text='Digite sua senha atual para confirmar'
    )
    
    new_password = forms.CharField(
        label='Nova Senha',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite a nova senha'
        }),
        min_length=8,
        help_text='Mínimo 8 caracteres'
    )
    
    confirm_password = forms.CharField(
        label='Confirmar Nova Senha',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Digite a nova senha novamente'
        }),
        help_text='Digite a nova senha novamente'
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_current_password(self):
        """Valida a senha atual"""
        current_password = self.cleaned_data.get('current_password')
        if not self.user.check_password(current_password):
            raise ValidationError('Senha atual incorreta')
        return current_password
    
    def clean(self):
        """Valida se as senhas coincidem"""
        cleaned_data = super().clean()
        new_password = cleaned_data.get('new_password')
        confirm_password = cleaned_data.get('confirm_password')
        
        if new_password and confirm_password:
            if new_password != confirm_password:
                raise ValidationError('As senhas não coincidem')
        
        return cleaned_data
    
    def save(self):
        """Salva a nova senha"""
        new_password = self.cleaned_data['new_password']
        self.user.set_password(new_password)
        self.user.save()
        
        # Log da alteração
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Senha alterada para usuário {self.user.cpf}")
        
        return self.user
