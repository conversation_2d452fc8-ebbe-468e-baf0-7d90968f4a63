{% extends 'base.html' %}

{% block title %}Dashboard - Portal do Cliente{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block extra_css %}
<style>
/* CSS específico para corrigir bordas dos botões de ação rápida */
.card-body .btn-outline-primary,
.card-body .btn-outline-success,
.card-body .btn-outline-warning,
.card-body .btn-outline-info {
    border-width: 1px !important;
    border-style: solid !important;
}

.card-body .btn-outline-success {
    border-color: #198754 !important;
    color: #198754 !important;
}

.card-body .btn-outline-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

.card-body .btn-outline-info {
    border-color: #0dcaf0 !important;
    color: #0dcaf0 !important;
}
</style>
{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h2 class="mb-1"><PERSON><PERSON><PERSON>, {{ user.first_name }}! 👋</h2>
                    <p class="text-muted mb-0">Bem-vindo de volta ao seu portal</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">Último acesso: Hoje às 14:30</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cards de Resumo -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Status da Conexão</h6>
                            <h4 class="mb-0">Ativo</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wifi fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Faturas em Dia</h6>
                            <h4 class="mb-0">✓</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Plano Atual</h6>
                            <h4 class="mb-0">100MB</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tachometer-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Chamados Abertos</h6>
                            <h4 class="mb-0">0</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-headset fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Conteúdo Principal -->
    <div class="row">
        <!-- Próximos Vencimentos -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Próximos Vencimentos
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Nenhuma fatura pendente no momento.
                    </div>
                    <a href="{% url 'billing:invoice_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-file-invoice me-2"></i>Ver Todas as Faturas
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Informações da Conta -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>Informações da Conta
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Nome:</strong><br>
                            {{ user.full_name }}
                        </div>
                        <div class="col-sm-6">
                            <strong>CPF:</strong><br>
                            {{ user.cpf }}
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Email:</strong><br>
                            {{ user.email }}
                        </div>
                        <div class="col-sm-6">
                            <strong>Telefone:</strong><br>
                            {{ user.phone|default:"Não informado" }}
                        </div>
                    </div>
                    <hr>
                    <a href="{% url 'customer_portal:profile' %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Editar Perfil
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Ações Rápidas -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Ações Rápidas
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'billing:invoice_list' %}" class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-download fa-2x d-block mb-2"></i>
                                Ver Faturas
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'connections:dashboard' %}" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-chart-line fa-2x d-block mb-2"></i>
                                Ver Consumo
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'support:ticket_create' %}" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-plus fa-2x d-block mb-2"></i>
                                Abrir Chamado
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'support:ticket_list' %}" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-history fa-2x d-block mb-2"></i>
                                Histórico
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
