"""
Views para o sistema de suporte e chamados.

Este módulo implementa as views para gerenciamento de tickets,
seguindo as melhores práticas de Django e padrões de design.
"""

from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone
from django.views.generic import CreateView, DetailView, ListView
from django.urls import reverse_lazy
from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from typing import Dict, Any
import logging
import os

from integrations.services import SupportService, CustomerService, IXCSoftAPIError
from .models import Ticket
from .forms import (
    TicketCreateForm, TicketResponseForm, TicketAttachmentForm,
    TicketFilterForm, TicketSearchForm
)

logger = logging.getLogger(__name__)


class TicketListView(LoginRequiredMixin, ListView):
    """
    View para listagem de tickets do cliente.

    Implementa filtros, busca e paginação para melhor UX.
    """

    model = Ticket
    template_name = 'support/ticket_list.html'
    context_object_name = 'tickets'
    paginate_by = 10

    def get_queryset(self):
        """Retorna queryset filtrado dos tickets do usuário"""
        queryset = Ticket.objects.filter(
            customer=self.request.user
        ).select_related('subject').prefetch_related('responses')

        # Aplica filtros do formulário
        form = TicketFilterForm(self.request.GET)
        if form.is_valid():
            if form.cleaned_data.get('status'):
                queryset = queryset.filter(status=form.cleaned_data['status'])

            if form.cleaned_data.get('priority'):
                queryset = queryset.filter(priority=form.cleaned_data['priority'])

            if form.cleaned_data.get('ticket_type'):
                queryset = queryset.filter(ticket_type=form.cleaned_data['ticket_type'])

            if form.cleaned_data.get('date_from'):
                queryset = queryset.filter(created_at__date__gte=form.cleaned_data['date_from'])

            if form.cleaned_data.get('date_to'):
                queryset = queryset.filter(created_at__date__lte=form.cleaned_data['date_to'])

        # Aplica busca textual
        search_form = TicketSearchForm(self.request.GET)
        if search_form.is_valid():
            query = search_form.cleaned_data['query']
            queryset = queryset.filter(
                Q(title__icontains=query) |
                Q(description__icontains=query) |
                Q(ixc_ticket_id__icontains=query)
            )

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        """Adiciona contexto extra para o template"""
        context = super().get_context_data(**kwargs)

        # Formulários de filtro e busca
        context['filter_form'] = TicketFilterForm(self.request.GET)
        context['search_form'] = TicketSearchForm(self.request.GET)

        # Estatísticas dos tickets
        user_tickets = Ticket.objects.filter(customer=self.request.user)
        context['stats'] = {
            'total': user_tickets.count(),
            'open': user_tickets.filter(status__in=['aberto', 'em_andamento']).count(),
            'waiting': user_tickets.filter(status='aguardando_cliente').count(),
            'resolved': user_tickets.filter(status='resolvido').count(),
        }

        context['title'] = 'Meus Chamados'
        return context


class TicketDetailView(LoginRequiredMixin, DetailView):
    """
    View para detalhes de um ticket específico.

    Mostra histórico completo de interações e permite respostas.
    """

    model = Ticket
    template_name = 'support/ticket_detail.html'
    context_object_name = 'ticket'

    def get_queryset(self):
        """Garante que o usuário só vê seus próprios tickets"""
        return Ticket.objects.filter(
            customer=self.request.user
        ).select_related('subject').prefetch_related(
            'responses__author',
            'attachments'
        )

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        """Adiciona formulários e dados extras ao contexto"""
        context = super().get_context_data(**kwargs)

        # Formulário para nova resposta
        context['response_form'] = TicketResponseForm()
        context['attachment_form'] = TicketAttachmentForm()

        # Respostas ordenadas cronologicamente
        context['responses'] = self.object.responses.filter(
            is_internal=False
        ).order_by('created_at')

        context['title'] = f'Chamado #{self.object.ixc_ticket_id}'
        return context


class TicketCreateView(LoginRequiredMixin, CreateView):
    """
    View para criação de novos tickets.

    Integra com a API da IXCSoft para criar o ticket remotamente.
    """

    model = Ticket
    form_class = TicketCreateForm
    template_name = 'support/ticket_create.html'
    success_url = reverse_lazy('support:ticket_list')

    def form_valid(self, form):
        """Processa criação do ticket local"""
        try:
            # Configura dados do cliente
            form.instance.customer = self.request.user

            # Gera um ID simulado para o ticket (em produção seria da API)
            import random
            form.instance.ixc_ticket_id = random.randint(1000, 9999)

            # Salva ticket local
            response = super().form_valid(form)

            # Processa arquivos anexados
            attachments = []
            for key in self.request.FILES:
                if key.startswith('attachment_'):
                    attachments.append(self.request.FILES[key])

            if attachments:
                from .models import TicketAttachment
                for file in attachments:
                    TicketAttachment.objects.create(
                        ticket=form.instance,
                        file=file,
                        uploaded_by=self.request.user,
                        file_name=file.name,
                        file_size=file.size
                    )

            # Criar notificação
            from customer_portal.models import Notification
            attachment_count = len(attachments) if attachments else 0
            attachment_text = f" com {attachment_count} anexo(s)" if attachment_count > 0 else ""

            Notification.create_notification(
                user=self.request.user,
                title='Chamado Criado',
                message=f'Seu chamado #{form.instance.ixc_ticket_id} foi criado com sucesso{attachment_text}!',
                type='success',
                category='support',
                url=f'/suporte/{form.instance.id}/'
            )

            messages.success(
                self.request,
                f'Chamado #{form.instance.ixc_ticket_id} criado com sucesso{attachment_text}! '
                'Nossa equipe entrará em contato em breve.'
            )

            logger.info(
                f"Ticket {form.instance.ixc_ticket_id} criado para "
                f"cliente {self.request.user.cpf} com {attachment_count} anexos"
            )

            return response

        except Exception as e:
            logger.error(f"Erro ao criar ticket: {e}")
            messages.error(
                self.request,
                'Erro interno do sistema. Tente novamente.'
            )
            return self.form_invalid(form)

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        """Adiciona contexto extra para o template"""
        context = super().get_context_data(**kwargs)
        context['title'] = 'Abrir Novo Chamado'
        return context


@login_required
def add_ticket_response(request, ticket_id):
    """
    View para adicionar resposta a um ticket.

    Permite ao cliente responder a um ticket existente
    com texto e anexos opcionais.
    """
    ticket = get_object_or_404(Ticket, id=ticket_id, customer=request.user)

    if request.method == 'POST':
        try:
            response_text = request.POST.get('response_text', '').strip()

            if not response_text:
                return JsonResponse({
                    'success': False,
                    'error': 'Texto da resposta é obrigatório'
                })

            # Criar resposta local
            from .models import TicketResponse
            response = TicketResponse.objects.create(
                ticket=ticket,
                author=request.user,
                message=response_text,
                is_from_customer=True
            )

            # Tentar enviar para API IXCSoft
            try:
                support_service = SupportService()
                api_response = support_service.add_ticket_response(
                    ticket.ixc_ticket_id,
                    response_text,
                    request.user.ixc_customer_id
                )

                if api_response:
                    response.ixc_response_id = api_response.get('id')
                    response.save()

            except IXCSoftAPIError as e:
                logger.warning(f"Erro ao enviar resposta para API: {e}")
                # Manter resposta local mesmo se API falhar

            # Processar anexos se houver
            attachments = []
            for file_key in request.FILES:
                if file_key.startswith('attachment_'):
                    file_obj = request.FILES[file_key]
                    attachment = save_ticket_attachment(response, file_obj)
                    if attachment:
                        attachments.append(attachment)

            # Criar notificação
            from customer_portal.models import Notification
            Notification.create_notification(
                user=request.user,
                title='Resposta Enviada',
                message=f'Sua resposta ao ticket #{ticket.id} foi enviada com sucesso.',
                type='success',
                category='support',
                url=f'/suporte/{ticket.id}/'
            )

            return JsonResponse({
                'success': True,
                'message': 'Resposta enviada com sucesso!',
                'response_id': response.id,
                'attachments_count': len(attachments)
            })

        except Exception as e:
            logger.error(f"Erro ao adicionar resposta: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erro interno do servidor'
            })

    return JsonResponse({'success': False, 'error': 'Método não permitido'})


@login_required
def upload_ticket_attachment(request, ticket_id):
    """
    View para upload de anexos em tickets.

    Permite upload de arquivos para anexar a tickets
    com validações de segurança.
    """
    ticket = get_object_or_404(Ticket, id=ticket_id, customer=request.user)

    if request.method == 'POST' and request.FILES.get('file'):
        try:
            file_obj = request.FILES['file']

            # Validações de segurança
            if not validate_attachment_file(file_obj):
                return JsonResponse({
                    'success': False,
                    'error': 'Tipo de arquivo não permitido'
                })

            # Salvar anexo
            from .models import TicketAttachment
            attachment = TicketAttachment.objects.create(
                ticket=ticket,
                file=file_obj,
                filename=file_obj.name,
                file_size=file_obj.size,
                uploaded_by=request.user
            )

            return JsonResponse({
                'success': True,
                'attachment_id': attachment.id,
                'filename': attachment.filename,
                'file_size': attachment.file_size,
                'download_url': f'/suporte/anexo/{attachment.id}/download/'
            })

        except Exception as e:
            logger.error(f"Erro ao fazer upload de anexo: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erro ao fazer upload do arquivo'
            })

    return JsonResponse({'success': False, 'error': 'Nenhum arquivo enviado'})


@login_required
def download_ticket_attachment(request, attachment_id):
    """
    View para download de anexos de tickets.

    Permite download seguro de anexos com verificação
    de permissões do usuário.
    """
    try:
        from .models import TicketAttachment
        attachment = get_object_or_404(
            TicketAttachment,
            id=attachment_id,
            ticket__customer=request.user
        )

        # Verificar se arquivo existe
        if not attachment.file or not os.path.exists(attachment.file.path):
            raise Http404("Arquivo não encontrado")

        # Preparar resposta para download
        response = HttpResponse(
            attachment.file.read(),
            content_type='application/octet-stream'
        )
        response['Content-Disposition'] = f'attachment; filename="{attachment.filename}"'
        response['Content-Length'] = attachment.file_size

        return response

    except Exception as e:
        logger.error(f"Erro ao fazer download de anexo: {e}")
        raise Http404("Arquivo não encontrado")


def validate_attachment_file(file_obj):
    """
    Valida arquivo de anexo para segurança.

    Verifica tipo, tamanho e extensão do arquivo
    para prevenir uploads maliciosos.
    """
    # Tamanho máximo: 10MB
    max_size = 10 * 1024 * 1024
    if file_obj.size > max_size:
        return False

    # Extensões permitidas
    allowed_extensions = [
        '.pdf', '.doc', '.docx', '.txt', '.rtf',
        '.jpg', '.jpeg', '.png', '.gif', '.bmp',
        '.zip', '.rar', '.7z',
        '.xls', '.xlsx', '.csv'
    ]

    file_extension = os.path.splitext(file_obj.name)[1].lower()
    if file_extension not in allowed_extensions:
        return False

    return True


def save_ticket_attachment(response, file_obj):
    """
    Salva anexo de resposta de ticket.

    Helper function para salvar anexos associados
    a respostas de tickets.
    """
    try:
        if not validate_attachment_file(file_obj):
            return None

        from .models import TicketAttachment
        attachment = TicketAttachment.objects.create(
            ticket=response.ticket,
            response=response,
            file=file_obj,
            filename=file_obj.name,
            file_size=file_obj.size,
            uploaded_by=response.author
        )

        return attachment

    except Exception as e:
        logger.error(f"Erro ao salvar anexo: {e}")
        return None
