#!/usr/bin/env python
"""
Script de teste para o formulário de criação de tickets melhorado.
Execute: python manage.py shell < test_ticket_form.py
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'provedor.settings')
django.setup()

from django.contrib.auth import get_user_model
from support.models import Ticket, TicketSubject
from support.forms import TicketCreateForm

User = get_user_model()

def test_ticket_form():
    """Testa o formulário de criação de tickets"""
    
    print("🧪 Testando formulário de criação de tickets...")
    
    # Criar um assunto de teste se não existir
    subject, created = TicketSubject.objects.get_or_create(
        ixc_subject_id=1,
        defaults={
            'name': 'Problema de Conexão',
            'description': 'Problemas relacionados à conectividade',
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ Assunto criado: {subject.name}")
    else:
        print(f"✅ Assunto encontrado: {subject.name}")
    
    # Teste 1: Formulário com assunto padrão
    print("\n📝 Teste 1: Formulário com assunto padrão")
    form_data = {
        'subject': subject.id,
        'title': 'Internet não funciona',
        'description': 'Minha internet parou de funcionar desde ontem à noite. Já tentei reiniciar o roteador mas não resolveu.',
        'priority': 'alta',
        'ticket_type': 'suporte',
        'problem_occurred_at': '2025-06-28T10:00',
        'affected_equipment': 'Roteador TP-Link',
        'attempted_solutions': 'Reiniciei o roteador, verifiquei cabos',
        'operating_system': 'Windows 11',
        'connection_type': 'fibra'
    }
    
    form = TicketCreateForm(data=form_data)
    if form.is_valid():
        print("✅ Formulário válido com assunto padrão")
    else:
        print("❌ Formulário inválido:")
        for field, errors in form.errors.items():
            print(f"  - {field}: {errors}")
    
    # Teste 2: Formulário com assunto personalizado
    print("\n📝 Teste 2: Formulário com assunto personalizado")
    form_data_custom = {
        'use_custom_subject': True,
        'custom_subject': 'Problema específico com VoIP',
        'custom_subject_reason': 'O problema é específico com telefonia VoIP que não está listado nos assuntos disponíveis',
        'title': 'Telefone VoIP não recebe chamadas',
        'description': 'O telefone VoIP consegue fazer ligações mas não recebe chamadas externas. O problema começou após a última atualização.',
        'priority': 'normal',
        'ticket_type': 'suporte',
        'error_message': 'SIP Registration Failed - Error 408',
        'steps_to_reproduce': '1. Aguardar chamada externa\n2. Telefone não toca\n3. Chamada vai direto para caixa postal',
        'impact_description': 'Não consigo receber chamadas importantes de clientes',
        'operating_system': 'Firmware do telefone v2.1.3',
        'connection_type': 'fibra'
    }
    
    form_custom = TicketCreateForm(data=form_data_custom)
    if form_custom.is_valid():
        print("✅ Formulário válido com assunto personalizado")
    else:
        print("❌ Formulário inválido:")
        for field, errors in form_custom.errors.items():
            print(f"  - {field}: {errors}")
    
    # Teste 3: Formulário inválido (sem assunto)
    print("\n📝 Teste 3: Formulário inválido (sem assunto)")
    form_data_invalid = {
        'title': 'Problema sem assunto',
        'description': 'Descrição do problema',
        'priority': 'normal',
        'ticket_type': 'suporte'
    }
    
    form_invalid = TicketCreateForm(data=form_data_invalid)
    if not form_invalid.is_valid():
        print("✅ Formulário corretamente inválido (sem assunto)")
        print("   Erros esperados:")
        for field, errors in form_invalid.errors.items():
            print(f"  - {field}: {errors}")
    else:
        print("❌ Formulário deveria ser inválido")
    
    print("\n🎉 Testes concluídos!")

if __name__ == "__main__":
    test_ticket_form()
