{% extends 'base.html' %}

{% block title %}Ticket #{{ ticket.id }} - Portal do Cliente{% endblock %}
{% block page_title %}Chamado de Suporte{% endblock %}

{% block extra_css %}
<style>
.ticket-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.ticket-status {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.status-open { background: var(--warning-color); color: white; }
.status-in-progress { background: var(--info-color); color: white; }
.status-resolved { background: var(--success-color); color: white; }
.status-closed { background: var(--gray-500); color: white; }

.priority-high { color: var(--danger-color); }
.priority-medium { color: var(--warning-color); }
.priority-low { color: var(--success-color); }

.response-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--gray-200);
}

.response-item.customer {
    border-left-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.02);
}

.response-item.support {
    border-left-color: var(--success-color);
    background: rgba(16, 185, 129, 0.02);
}

.response-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.response-author {
    font-weight: 600;
    color: var(--gray-800);
}

.response-date {
    font-size: 0.875rem;
    color: var(--gray-500);
}

.response-content {
    color: var(--gray-700);
    line-height: 1.6;
}

.attachment-item {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-sm);
    margin: 0.25rem;
    text-decoration: none;
    color: var(--gray-700);
    transition: all 0.2s ease;
}

.attachment-item:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.response-form {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    margin-top: 2rem;
}

.file-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.uploaded-files {
    margin-top: 1rem;
}

.uploaded-file {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Ticket Header -->
    <div class="ticket-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center mb-2">
                    <h2 class="mb-0 me-3">Ticket #{{ ticket.id }}</h2>
                    <span class="ticket-status status-{{ ticket.status|lower }}">
                        {{ ticket.get_status_display }}
                    </span>
                </div>
                <h4 class="mb-1">{{ ticket.title }}</h4>
                <p class="mb-0 opacity-75">{{ ticket.subject.name }}</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="mb-2">
                    <strong>Prioridade:</strong>
                    <span class="priority-{{ ticket.priority|lower }}">
                        <i class="fas fa-flag me-1"></i>{{ ticket.get_priority_display }}
                    </span>
                </div>
                <div class="mb-2">
                    <strong>Criado:</strong> {{ ticket.created_at|date:"d/m/Y H:i" }}
                </div>
                {% if ticket.updated_at != ticket.created_at %}
                <div>
                    <strong>Atualizado:</strong> {{ ticket.updated_at|date:"d/m/Y H:i" }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Ticket Description -->
    <div class="response-item">
        <div class="response-header">
            <div>
                <div class="response-author">
                    <i class="fas fa-user me-2"></i>{{ ticket.customer.first_name }} {{ ticket.customer.last_name }}
                </div>
                <div class="response-date">{{ ticket.created_at|date:"d/m/Y H:i" }}</div>
            </div>
            <div>
                <span class="badge bg-primary">Descrição Inicial</span>
            </div>
        </div>
        <div class="response-content">
            {{ ticket.description|linebreaks }}
        </div>
    </div>
    
    <!-- Responses -->
    {% for response in ticket.responses.all %}
    <div class="response-item {% if response.is_from_customer %}customer{% else %}support{% endif %}">
        <div class="response-header">
            <div>
                <div class="response-author">
                    {% if response.is_from_customer %}
                        <i class="fas fa-user me-2"></i>{{ response.author.first_name }} {{ response.author.last_name }}
                    {% else %}
                        <i class="fas fa-headset me-2"></i>Suporte Técnico
                    {% endif %}
                </div>
                <div class="response-date">{{ response.created_at|date:"d/m/Y H:i" }}</div>
            </div>
            <div>
                {% if response.is_from_customer %}
                    <span class="badge bg-primary">Cliente</span>
                {% else %}
                    <span class="badge bg-success">Suporte</span>
                {% endif %}
            </div>
        </div>
        <div class="response-content">
            {{ response.message|linebreaks }}
        </div>
        
        <!-- Attachments -->
        {% if response.attachments.exists %}
        <div class="mt-3">
            <strong class="text-muted">Anexos:</strong>
            <div class="mt-2">
                {% for attachment in response.attachments.all %}
                <a href="{% url 'support:download_attachment' attachment.id %}" class="attachment-item">
                    <i class="fas fa-paperclip me-2"></i>
                    {{ attachment.filename }}
                    <small class="ms-2 text-muted">({{ attachment.file_size|filesizeformat }})</small>
                </a>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    {% endfor %}
    
    <!-- Response Form -->
    {% if ticket.status != 'closed' %}
    <div class="response-form">
        <h5><i class="fas fa-reply me-2"></i>Adicionar Resposta</h5>
        
        <form id="responseForm" method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="responseText" class="form-label">Sua Resposta</label>
                <textarea id="responseText" name="response_text" class="form-control" rows="5" 
                          placeholder="Digite sua resposta aqui..." required></textarea>
            </div>
            
            <!-- File Upload Area -->
            <div class="mb-3">
                <label class="form-label">Anexos (opcional)</label>
                <div class="file-upload-area" id="fileUploadArea">
                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                    <p class="mb-2">Clique aqui ou arraste arquivos para anexar</p>
                    <small class="text-muted">Máximo 10MB por arquivo. Formatos: PDF, DOC, IMG, ZIP</small>
                    <input type="file" id="fileInput" multiple style="display: none;" 
                           accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar">
                </div>
                <div id="uploadedFiles" class="uploaded-files"></div>
            </div>
            
            <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'support:ticket_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Voltar à Lista
                </a>
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <i class="fas fa-paper-plane me-2"></i>Enviar Resposta
                </button>
            </div>
        </form>
    </div>
    {% else %}
    <div class="response-form text-center">
        <i class="fas fa-lock fa-3x text-muted mb-3"></i>
        <h5>Ticket Fechado</h5>
        <p class="text-muted">Este ticket foi fechado e não aceita mais respostas.</p>
        <a href="{% url 'support:ticket_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Voltar à Lista
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const responseForm = document.getElementById('responseForm');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadedFiles = document.getElementById('uploadedFiles');
    const submitBtn = document.getElementById('submitBtn');
    
    let selectedFiles = [];
    
    // File upload handling
    fileUploadArea.addEventListener('click', () => fileInput.click());
    
    fileUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });
    
    fileUploadArea.addEventListener('dragleave', () => {
        fileUploadArea.classList.remove('dragover');
    });
    
    fileUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });
    
    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
    
    function handleFiles(files) {
        for (let file of files) {
            if (validateFile(file)) {
                selectedFiles.push(file);
                displayFile(file);
            }
        }
    }
    
    function validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'image/jpeg',
            'image/png',
            'image/gif',
            'application/zip',
            'application/x-rar-compressed'
        ];
        
        if (file.size > maxSize) {
            alert('Arquivo muito grande. Máximo 10MB.');
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            alert('Tipo de arquivo não permitido.');
            return false;
        }
        
        return true;
    }
    
    function displayFile(file) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'uploaded-file';
        fileDiv.innerHTML = `
            <div>
                <i class="fas fa-file me-2"></i>
                <span>${file.name}</span>
                <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile('${file.name}')">
                <i class="fas fa-times"></i>
            </button>
        `;
        uploadedFiles.appendChild(fileDiv);
    }
    
    window.removeFile = function(fileName) {
        selectedFiles = selectedFiles.filter(f => f.name !== fileName);
        updateFileDisplay();
    }
    
    function updateFileDisplay() {
        uploadedFiles.innerHTML = '';
        selectedFiles.forEach(file => displayFile(file));
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Form submission
    responseForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const responseText = document.getElementById('responseText').value.trim();
        if (!responseText) {
            alert('Por favor, digite uma resposta.');
            return;
        }
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
        
        const formData = new FormData();
        formData.append('response_text', responseText);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        
        // Add files
        selectedFiles.forEach((file, index) => {
            formData.append(`attachment_${index}`, file);
        });
        
        fetch(`{% url 'support:add_response' ticket.id %}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Resposta enviada com sucesso!');
                location.reload();
            } else {
                alert('Erro: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro de conexão. Tente novamente.');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Enviar Resposta';
        });
    });
});
</script>
{% endblock %}
