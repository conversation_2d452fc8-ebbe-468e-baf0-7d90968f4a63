"""
Admin para o sistema de suporte.

Configurações do Django Admin para gerenciamento
de tickets, respostas e anexos.
"""

from django.contrib import admin
from .models import Ticket, TicketSubject, TicketResponse, TicketAttachment


@admin.register(TicketSubject)
class TicketSubjectAdmin(admin.ModelAdmin):
    """Admin para assuntos de tickets"""
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')


class TicketResponseInline(admin.TabularInline):
    """Inline para respostas do ticket"""
    model = TicketResponse
    extra = 0
    readonly_fields = ('created_at',)


class TicketAttachmentInline(admin.TabularInline):
    """Inline para anexos do ticket"""
    model = TicketAttachment
    extra = 0
    readonly_fields = ('uploaded_at', 'file_size')


@admin.register(Ticket)
class TicketAdmin(admin.ModelAdmin):
    """Admin para tickets de suporte"""
    list_display = (
        'ixc_ticket_id', 'customer', 'subject', 'title',
        'status', 'priority', 'created_at'
    )
    list_filter = ('status', 'priority', 'ticket_type', 'created_at', 'subject')
    search_fields = (
        'ixc_ticket_id', 'title', 'description',
        'customer__cpf', 'customer__first_name'
    )
    readonly_fields = ('created_at', 'updated_at', 'days_open')
    date_hierarchy = 'created_at'
    inlines = [TicketResponseInline, TicketAttachmentInline]

    fieldsets = (
        ('Informações Básicas', {
            'fields': ('ixc_ticket_id', 'customer', 'subject', 'title')
        }),
        ('Descrição', {
            'fields': ('description',)
        }),
        ('Classificação', {
            'fields': ('status', 'priority', 'ticket_type')
        }),
        ('Atendimento', {
            'fields': ('assigned_technician', 'estimated_resolution')
        }),
        ('Datas', {
            'fields': ('created_at', 'updated_at', 'resolved_at', 'closed_at', 'days_open')
        }),
    )


@admin.register(TicketResponse)
class TicketResponseAdmin(admin.ModelAdmin):
    """Admin para respostas de tickets"""
    list_display = (
        'ticket', 'author_name', 'response_type',
        'is_solution', 'created_at'
    )
    list_filter = ('response_type', 'is_solution', 'is_internal', 'created_at')
    search_fields = ('ticket__ixc_ticket_id', 'message', 'author_name')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'


@admin.register(TicketAttachment)
class TicketAttachmentAdmin(admin.ModelAdmin):
    """Admin para anexos de tickets"""
    list_display = (
        'ticket', 'original_filename', 'file_size_formatted',
        'uploaded_by', 'uploaded_at'
    )
    list_filter = ('content_type', 'uploaded_at')
    search_fields = ('ticket__ixc_ticket_id', 'original_filename')
    readonly_fields = ('uploaded_at', 'file_size', 'content_type')
