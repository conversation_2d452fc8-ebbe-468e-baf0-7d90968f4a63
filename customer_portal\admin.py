"""
Admin para o portal do cliente.

Configurações do Django Admin para gerenciamento
de notificações e outras funcionalidades.
"""

from django.contrib import admin
from .models import Notification


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin para notificações"""
    list_display = (
        'title', 'user', 'type', 'category', 'is_read', 'created_at'
    )
    list_filter = ('type', 'category', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'user__first_name', 'user__cpf')
    readonly_fields = ('created_at', 'read_at')
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Informações Básicas', {
            'fields': ('user', 'title', 'message')
        }),
        ('Classificação', {
            'fields': ('type', 'category')
        }),
        ('Status', {
            'fields': ('is_read', 'read_at')
        }),
        ('Ação', {
            'fields': ('url',)
        }),
        ('Datas', {
            'fields': ('created_at', 'expires_at')
        }),
    )

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        """Marca notificações como lidas"""
        updated = queryset.update(is_read=True)
        self.message_user(
            request,
            f'{updated} notificação(ões) marcada(s) como lida(s).'
        )
    mark_as_read.short_description = "Marcar como lida"

    def mark_as_unread(self, request, queryset):
        """Marca notificações como não lidas"""
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(
            request,
            f'{updated} notificação(ões) marcada(s) como não lida(s).'
        )
    mark_as_unread.short_description = "Marcar como não lida"
