"""
URLs para o sistema de suporte.

Define as rotas para todas as funcionalidades de tickets,
seguindo padrões RESTful e convenções do Django.
"""

from django.urls import path
from . import views

app_name = 'support'

urlpatterns = [
    # Listagem e criação de tickets
    path('', views.TicketListView.as_view(), name='ticket_list'),
    path('novo/', views.TicketCreateView.as_view(), name='ticket_create'),
    
    # Detalhes e interações com tickets
    path('<int:pk>/', views.TicketDetailView.as_view(), name='ticket_detail'),

    # Respostas e anexos
    path('<int:ticket_id>/responder/', views.add_ticket_response, name='add_response'),
    path('<int:ticket_id>/anexar/', views.upload_ticket_attachment, name='add_attachment'),
    path('anexo/<int:attachment_id>/download/', views.download_ticket_attachment, name='download_attachment'),
]
