# RAILWAY EMERGENCY FIX - FORCE ALLOWED_HOSTS
# Este arquivo força o ALLOWED_HOSTS no Railway

import os
import django
from django.conf import settings

# Força ALLOWED_HOSTS antes de qualquer coisa
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'provedor.settings')

# Força a configuração
def force_allowed_hosts():
    """Força ALLOWED_HOSTS para Railway"""
    if hasattr(settings, 'ALLOWED_HOSTS'):
        settings.ALLOWED_HOSTS = ['*']
        print(f"FORCED ALLOWED_HOSTS to: {settings.ALLOWED_HOSTS}")
    
# Executa a correção
if __name__ == "__main__":
    django.setup()
    force_allowed_hosts()
