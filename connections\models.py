"""
Modelos para o sistema de conexões e uso de internet.

Este módulo contém os modelos que representam dados de conexão,
uso de banda, testes de velocidade e estatísticas de rede.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator

User = get_user_model()


class ConnectionSession(models.Model):
    """
    Sessão de conexão do cliente.

    Representa uma sessão de conexão individual registrada
    pelo sistema Radius da IXCSoft.
    """

    STATUS_CHOICES = [
        ('active', 'Ativa'),
        ('terminated', 'Finalizada'),
        ('timeout', 'Timeout'),
        ('error', 'Erro'),
    ]

    # Dados da IXCSoft
    ixc_session_id = models.CharField(
        max_length=100,
        unique=True,
        help_text='ID da sessão na IXCSoft'
    )
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='connection_sessions',
        help_text='Cliente da sessão'
    )

    # Dados da conexão
    start_time = models.DateTimeField(
        help_text='In<PERSON><PERSON> da sessão'
    )
    end_time = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Fim da sessão'
    )
    duration_seconds = models.PositiveIntegerField(
        default=0,
        help_text='Duração em segundos'
    )

    # Dados de rede
    ip_address = models.GenericIPAddressField(
        help_text='Endereço IP atribuído'
    )
    mac_address = models.CharField(
        max_length=17,
        blank=True,
        help_text='Endereço MAC do dispositivo'
    )
    nas_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text='IP do NAS (Network Access Server)'
    )

    # Dados de tráfego
    bytes_uploaded = models.BigIntegerField(
        default=0,
        help_text='Bytes enviados (upload)'
    )
    bytes_downloaded = models.BigIntegerField(
        default=0,
        help_text='Bytes recebidos (download)'
    )
    packets_uploaded = models.BigIntegerField(
        default=0,
        help_text='Pacotes enviados'
    )
    packets_downloaded = models.BigIntegerField(
        default=0,
        help_text='Pacotes recebidos'
    )

    # Status e controle
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        help_text='Status da sessão'
    )
    termination_cause = models.CharField(
        max_length=100,
        blank=True,
        help_text='Causa da terminação'
    )

    # Controle de cache
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Sessão de Conexão'
        verbose_name_plural = 'Sessões de Conexão'
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['customer', 'start_time']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
        ]

    def __str__(self) -> str:
        return f"Sessão {self.ixc_session_id} - {self.customer.full_name}"

    @property
    def total_bytes(self) -> int:
        """Total de bytes transferidos"""
        return self.bytes_uploaded + self.bytes_downloaded

    @property
    def total_mb(self) -> float:
        """Total em megabytes"""
        return self.total_bytes / (1024 * 1024)

    @property
    def total_gb(self) -> float:
        """Total em gigabytes"""
        return self.total_bytes / (1024 * 1024 * 1024)

    @property
    def duration_formatted(self) -> str:
        """Duração formatada"""
        if self.duration_seconds < 60:
            return f"{self.duration_seconds}s"
        elif self.duration_seconds < 3600:
            minutes = self.duration_seconds // 60
            seconds = self.duration_seconds % 60
            return f"{minutes}m {seconds}s"
        else:
            hours = self.duration_seconds // 3600
            minutes = (self.duration_seconds % 3600) // 60
            return f"{hours}h {minutes}m"

    @property
    def average_speed_mbps(self) -> float:
        """Velocidade média em Mbps"""
        if self.duration_seconds > 0:
            bits_per_second = (self.total_bytes * 8) / self.duration_seconds
            return bits_per_second / (1024 * 1024)
        return 0.0

    def calculate_duration(self) -> None:
        """Calcula duração da sessão"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            self.duration_seconds = int(delta.total_seconds())

    def save(self, *args, **kwargs):
        """Override save para calcular duração automaticamente"""
        if self.end_time and not self.duration_seconds:
            self.calculate_duration()
        super().save(*args, **kwargs)


class SpeedTest(models.Model):
    """
    Teste de velocidade realizado pelo cliente.

    Armazena resultados de testes de velocidade para
    monitoramento da qualidade da conexão.
    """

    # Dados da IXCSoft
    ixc_test_id = models.CharField(
        max_length=100,
        unique=True,
        help_text='ID do teste na IXCSoft'
    )
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='speed_tests',
        help_text='Cliente que realizou o teste'
    )

    # Dados do teste
    test_date = models.DateTimeField(
        help_text='Data e hora do teste'
    )
    server_name = models.CharField(
        max_length=100,
        blank=True,
        help_text='Nome do servidor de teste'
    )
    server_location = models.CharField(
        max_length=100,
        blank=True,
        help_text='Localização do servidor'
    )

    # Resultados de velocidade (em Mbps)
    download_speed = models.FloatField(
        validators=[MinValueValidator(0)],
        help_text='Velocidade de download em Mbps'
    )
    upload_speed = models.FloatField(
        validators=[MinValueValidator(0)],
        help_text='Velocidade de upload em Mbps'
    )
    ping = models.FloatField(
        validators=[MinValueValidator(0)],
        help_text='Latência (ping) em ms'
    )
    jitter = models.FloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        help_text='Jitter em ms'
    )

    # Dados do plano contratado (para comparação)
    contracted_download = models.FloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        help_text='Velocidade contratada de download em Mbps'
    )
    contracted_upload = models.FloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        help_text='Velocidade contratada de upload em Mbps'
    )

    # Dados técnicos
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text='IP usado no teste'
    )
    user_agent = models.TextField(
        blank=True,
        help_text='User agent do navegador'
    )

    # Controle
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Teste de Velocidade'
        verbose_name_plural = 'Testes de Velocidade'
        ordering = ['-test_date']
        indexes = [
            models.Index(fields=['customer', 'test_date']),
            models.Index(fields=['test_date']),
        ]

    def __str__(self) -> str:
        return f"Teste {self.test_date.strftime('%d/%m/%Y %H:%M')} - {self.customer.full_name}"

    @property
    def download_percentage(self) -> float:
        """Percentual da velocidade contratada (download)"""
        if self.contracted_download and self.contracted_download > 0:
            return (self.download_speed / self.contracted_download) * 100
        return 0.0

    @property
    def upload_percentage(self) -> float:
        """Percentual da velocidade contratada (upload)"""
        if self.contracted_upload and self.contracted_upload > 0:
            return (self.upload_speed / self.contracted_upload) * 100
        return 0.0

    @property
    def quality_rating(self) -> str:
        """Avaliação da qualidade baseada no percentual"""
        avg_percentage = (self.download_percentage + self.upload_percentage) / 2

        if avg_percentage >= 90:
            return 'Excelente'
        elif avg_percentage >= 70:
            return 'Boa'
        elif avg_percentage >= 50:
            return 'Regular'
        else:
            return 'Ruim'

    @property
    def quality_badge_class(self) -> str:
        """Classe CSS para badge de qualidade"""
        rating = self.quality_rating
        classes = {
            'Excelente': 'bg-success',
            'Boa': 'bg-primary',
            'Regular': 'bg-warning',
            'Ruim': 'bg-danger',
        }
        return classes.get(rating, 'bg-secondary')


class BandwidthUsage(models.Model):
    """
    Uso de banda por período.

    Armazena dados agregados de uso de banda para
    geração de relatórios e gráficos.
    """

    PERIOD_CHOICES = [
        ('hour', 'Hora'),
        ('day', 'Dia'),
        ('week', 'Semana'),
        ('month', 'Mês'),
    ]

    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bandwidth_usage',
        help_text='Cliente do uso'
    )

    # Período
    period_type = models.CharField(
        max_length=10,
        choices=PERIOD_CHOICES,
        help_text='Tipo de período'
    )
    period_start = models.DateTimeField(
        help_text='Início do período'
    )
    period_end = models.DateTimeField(
        help_text='Fim do período'
    )

    # Dados de uso (em bytes)
    bytes_uploaded = models.BigIntegerField(
        default=0,
        help_text='Total de bytes enviados'
    )
    bytes_downloaded = models.BigIntegerField(
        default=0,
        help_text='Total de bytes recebidos'
    )

    # Picos de velocidade (em bps)
    peak_upload_speed = models.BigIntegerField(
        default=0,
        help_text='Pico de velocidade de upload em bps'
    )
    peak_download_speed = models.BigIntegerField(
        default=0,
        help_text='Pico de velocidade de download em bps'
    )

    # Velocidade média (em bps)
    avg_upload_speed = models.BigIntegerField(
        default=0,
        help_text='Velocidade média de upload em bps'
    )
    avg_download_speed = models.BigIntegerField(
        default=0,
        help_text='Velocidade média de download em bps'
    )

    # Controle
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Uso de Banda'
        verbose_name_plural = 'Uso de Banda'
        ordering = ['-period_start']
        unique_together = ['customer', 'period_type', 'period_start']
        indexes = [
            models.Index(fields=['customer', 'period_type', 'period_start']),
            models.Index(fields=['period_start']),
        ]

    def __str__(self) -> str:
        return f"Uso {self.period_type} - {self.period_start.strftime('%d/%m/%Y')} - {self.customer.full_name}"

    @property
    def total_bytes(self) -> int:
        """Total de bytes transferidos"""
        return self.bytes_uploaded + self.bytes_downloaded

    @property
    def total_gb(self) -> float:
        """Total em gigabytes"""
        return self.total_bytes / (1024 ** 3)

    @property
    def peak_speed_mbps(self) -> float:
        """Pico de velocidade em Mbps (maior entre upload e download)"""
        peak_bps = max(self.peak_upload_speed, self.peak_download_speed)
        return peak_bps / (1024 ** 2)

    @property
    def avg_speed_mbps(self) -> float:
        """Velocidade média em Mbps (maior entre upload e download)"""
        avg_bps = max(self.avg_upload_speed, self.avg_download_speed)
        return avg_bps / (1024 ** 2)
