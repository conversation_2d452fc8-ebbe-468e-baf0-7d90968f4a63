# Portal do Cliente - Provedor de Internet

Sistema completo de portal para clientes de provedor de internet com integração à API da IXCSoft.

## 🚀 Funcionalidades

- ✅ **Autenticação com CPF** - Login seguro com validação algorítmica
- ✅ **Dashboard Interativo** - Visão geral da conta do cliente
- ✅ **Sistema de Faturas** - Visualização e download de boletos
- ✅ **Sistema de Suporte** - Tickets categorizados com respostas
- ✅ **Histórico de Conexões** - Gráficos de uso e testes de velocidade
- ✅ **Interface Responsiva** - Design mobile-first com Bootstrap 5

## 🛠️ Tecnologias

- **Backend:** Django 4.2.7 (Python)
- **Frontend:** Django Templates + Bootstrap 5
- **Banco de Dados:** SQLite (desenvolvimento) / PostgreSQL (produção)
- **API:** Integração com IXCSoft
- **Gráficos:** Chart.js
- **Notificações:** Firebase Cloud Messaging (preparado)

## 📋 Pré-requisitos

- Python 3.8+
- pip (gerenciador de pacotes Python)
- Git

## 🔧 Instalação

### 1. Clone o repositório
```bash
git clone <url-do-repositorio>
cd provedor
```

### 2. Crie um ambiente virtual
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows
```

### 3. Instale as dependências
```bash
pip install -r requirements.txt
```

### 4. Configure as variáveis de ambiente
Crie um arquivo `.env` na raiz do projeto:
```env
SECRET_KEY=sua-chave-secreta-aqui
DEBUG=True
IXCSOFT_API_URL=https://api.ixcsoft.com.br
IXCSOFT_API_TOKEN=seu-token-da-ixcsoft
FIREBASE_CREDENTIALS_PATH=path/to/firebase-credentials.json
```

### 5. Execute as migrações
```bash
python manage.py migrate
```

### 6. Crie um superusuário
```bash
python manage.py createsuperuser
```

### 7. Execute o servidor
```bash
python manage.py runserver
```

## 🌐 URLs Principais

- **Portal:** http://127.0.0.1:8000/
- **Login:** http://127.0.0.1:8000/auth/login/
- **Admin:** http://127.0.0.1:8000/admin/
- **Faturas:** http://127.0.0.1:8000/faturas/
- **Suporte:** http://127.0.0.1:8000/suporte/
- **Conexões:** http://127.0.0.1:8000/conexoes/

## 📊 Dados de Exemplo

Para criar dados de exemplo para demonstração:

```bash
# Criar faturas de exemplo
python manage.py create_sample_invoices

# Criar tickets de suporte de exemplo
python manage.py create_sample_tickets

# Criar dados de conexão de exemplo
python manage.py create_sample_connections
```

Para limpar todos os dados de exemplo:
```bash
python manage.py clear_sample_data --confirm
```

## 🔐 Credenciais Padrão

Após criar o superusuário, use as credenciais definidas para acessar o sistema.

## 📁 Estrutura do Projeto

```
provedor/
├── authentication/     # Sistema de autenticação
├── customer_portal/    # Dashboard principal
├── billing/           # Faturas e boletos
├── support/           # Sistema de suporte
├── connections/       # Histórico de conexões
├── integrations/      # APIs externas (IXCSoft)
├── notifications/     # Sistema de notificações
├── templates/         # Templates HTML
├── static/           # Arquivos estáticos (CSS, JS)
└── requirements.txt  # Dependências Python
```

## 🔧 Comandos Úteis

### Desenvolvimento
```bash
# Verificar problemas no código
python manage.py check

# Criar migrações
python manage.py makemigrations

# Aplicar migrações
python manage.py migrate

# Coletar arquivos estáticos
python manage.py collectstatic
```

### Dados de Exemplo
```bash
# Criar dados completos de exemplo
python manage.py create_sample_invoices
python manage.py create_sample_tickets
python manage.py create_sample_connections

# Limpar dados de exemplo
python manage.py clear_sample_data
```

## 🚀 Deploy em Produção

### 1. Configurações de Produção
- Altere `DEBUG = False` no settings.py
- Configure banco PostgreSQL
- Configure servidor web (Nginx + Gunicorn)
- Configure SSL/HTTPS

### 2. Variáveis de Ambiente
```env
SECRET_KEY=chave-secreta-forte
DEBUG=False
DATABASE_URL=postgresql://user:pass@host:port/dbname
IXCSOFT_API_URL=https://api.ixcsoft.com.br
IXCSOFT_API_TOKEN=token-producao
```

## 📞 Suporte

Para dúvidas ou problemas:
- Consulte a documentação do código (docstrings)
- Verifique os logs do Django
- Contate a equipe de desenvolvimento

## 📄 Licença

Este projeto é proprietário e confidencial.

---

**Desenvolvido com ❤️ seguindo as melhores práticas de Django**
