from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import datetime
import logging

from integrations.services import BillingService, CustomerService, IXCSoftAPIError
from .models import Invoice

logger = logging.getLogger(__name__)


@login_required
def invoice_list(request):
    """Lista de faturas do cliente"""
    try:
        # Busca dados do cliente na IXCSoft se necessário
        if not request.user.ixc_customer_id:
            customer_service = CustomerService()
            customer_data = customer_service.get_customer_by_cpf(request.user.cpf_clean)

            if customer_data:
                request.user.ixc_customer_id = customer_data.get('id')
                request.user.save(update_fields=['ixc_customer_id'])

        if not request.user.ixc_customer_id:
            messages.error(request, 'Não foi possível encontrar seus dados no sistema.')
            return render(request, 'billing/invoice_list.html', {'invoices': []})

        # Busca faturas via API
        billing_service = BillingService()
        api_invoices = billing_service.get_customer_invoices(request.user.ixc_customer_id)

        # Sincroniza com banco local (cache)
        sync_invoices_with_api(request.user, api_invoices)

        # Busca faturas do banco local
        invoices = Invoice.objects.filter(customer=request.user)

        # Filtros
        status_filter = request.GET.get('status')
        if status_filter:
            invoices = invoices.filter(status=status_filter)

        # Paginação
        paginator = Paginator(invoices, 10)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Estatísticas
        stats = {
            'total_invoices': invoices.count(),
            'pending_invoices': invoices.filter(status='aberto').count(),
            'overdue_invoices': invoices.filter(
                status='aberto',
                due_date__lt=timezone.now().date()
            ).count(),
            'total_pending_amount': sum(
                inv.amount for inv in invoices.filter(status='aberto')
            ),
        }

        context = {
            'page_obj': page_obj,
            'invoices': page_obj.object_list,
            'stats': stats,
            'status_filter': status_filter,
            'title': 'Minhas Faturas'
        }

        return render(request, 'billing/invoice_list.html', context)

    except IXCSoftAPIError as e:
        logger.error(f"Erro na API IXCSoft: {e}")
        messages.error(request, 'Erro ao carregar faturas. Tente novamente em alguns minutos.')
        return render(request, 'billing/invoice_list.html', {'invoices': []})

    except Exception as e:
        logger.error(f"Erro inesperado em invoice_list: {e}")
        messages.error(request, 'Erro interno. Contate o suporte.')
        return render(request, 'billing/invoice_list.html', {'invoices': []})


@login_required
def invoice_detail(request, invoice_id):
    """Detalhes de uma fatura específica"""
    try:
        invoice = get_object_or_404(Invoice, id=invoice_id, customer=request.user)

        # Busca detalhes atualizados da API se necessário
        billing_service = BillingService()
        api_invoice = billing_service.get_invoice_details(invoice.ixc_invoice_id)

        if api_invoice:
            # Atualiza dados locais se necessário
            update_invoice_from_api(invoice, api_invoice)

        context = {
            'invoice': invoice,
            'title': f'Fatura #{invoice.ixc_invoice_id}'
        }

        return render(request, 'billing/invoice_detail.html', context)

    except Exception as e:
        logger.error(f"Erro ao carregar detalhes da fatura {invoice_id}: {e}")
        messages.error(request, 'Erro ao carregar detalhes da fatura.')
        return redirect('billing:invoice_list')


@login_required
def download_invoice_pdf(request, invoice_id):
    """Download do PDF da fatura"""
    try:
        invoice = get_object_or_404(Invoice, id=invoice_id, customer=request.user)

        billing_service = BillingService()
        pdf_content = billing_service.get_invoice_pdf(invoice.ixc_invoice_id)

        if pdf_content:
            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="fatura_{invoice.ixc_invoice_id}.pdf"'
            return response
        else:
            messages.error(request, 'Não foi possível baixar o PDF da fatura.')
            return redirect('billing:invoice_detail', invoice_id=invoice_id)

    except Exception as e:
        logger.error(f"Erro ao baixar PDF da fatura {invoice_id}: {e}")
        messages.error(request, 'Erro ao baixar PDF da fatura.')
        return redirect('billing:invoice_list')


def sync_invoices_with_api(user, api_invoices):
    """Sincroniza faturas da API com banco local"""
    try:
        for api_invoice in api_invoices:
            invoice_id = api_invoice.get('id')
            if not invoice_id:
                continue

            # Busca ou cria fatura local
            invoice, created = Invoice.objects.get_or_create(
                ixc_invoice_id=invoice_id,
                customer=user,
                defaults={
                    'description': api_invoice.get('descricao', ''),
                    'amount': api_invoice.get('valor', 0),
                    'due_date': parse_date(api_invoice.get('vencimento')),
                    'issue_date': parse_date(api_invoice.get('emissao')),
                    'status': map_status(api_invoice.get('status')),
                    'barcode': api_invoice.get('codigo_barras', ''),
                    'digitable_line': api_invoice.get('linha_digitavel', ''),
                }
            )

            # Atualiza se não foi criado agora
            if not created:
                update_invoice_from_api(invoice, api_invoice)

    except Exception as e:
        logger.error(f"Erro ao sincronizar faturas: {e}")


def update_invoice_from_api(invoice, api_data):
    """Atualiza fatura local com dados da API"""
    try:
        invoice.description = api_data.get('descricao', invoice.description)
        invoice.amount = api_data.get('valor', invoice.amount)
        invoice.status = map_status(api_data.get('status'))
        invoice.barcode = api_data.get('codigo_barras', invoice.barcode)
        invoice.digitable_line = api_data.get('linha_digitavel', invoice.digitable_line)

        # Data de pagamento se pago
        if invoice.status == 'pago' and api_data.get('data_pagamento'):
            invoice.payment_date = parse_date(api_data.get('data_pagamento'))

        invoice.save()

    except Exception as e:
        logger.error(f"Erro ao atualizar fatura {invoice.id}: {e}")


def parse_date(date_string):
    """Converte string de data para objeto date"""
    if not date_string:
        return timezone.now().date()

    try:
        # Tenta diferentes formatos de data
        for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%Y-%m-%d %H:%M:%S']:
            try:
                return datetime.strptime(date_string, fmt).date()
            except ValueError:
                continue

        return timezone.now().date()

    except Exception:
        return timezone.now().date()


def map_status(api_status):
    """Mapeia status da API para status local"""
    status_map = {
        'A': 'aberto',
        'P': 'pago',
        'V': 'vencido',
        'C': 'cancelado',
        'aberto': 'aberto',
        'pago': 'pago',
        'vencido': 'vencido',
        'cancelado': 'cancelado',
    }

    return status_map.get(api_status, 'aberto')
